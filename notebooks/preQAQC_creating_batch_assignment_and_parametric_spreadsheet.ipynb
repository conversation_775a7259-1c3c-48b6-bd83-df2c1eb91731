{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ast\n", "import glob as glob\n", "import itertools\n", "import os\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define input paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Need to add your specific 179D Teams path in here\n", "# list_of_paths = [\n", "#     Path(\"~/NREL/179d - Documents/General/\").expanduser(),  # for J<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "#     Path(\"~/OneDrive - NREL/General - 179d/\").expanduser(),  # for Jie\n", "#     Path(\"~/Library/CloudStorage/OneDrive-NREL/General\").expanduser(),  # for Alex\n", "#     Path(\"~/Library/CloudStorage/OneDrive-SharedLibraries-NREL/179d - General\").expanduser(),  # for Carlo\n", "# ]\n", "\n", "# # Initialize variables\n", "# found_dir = False\n", "# DIR_179 = \"\"\n", "\n", "# # Find relevant 179D Teams path\n", "# for teams_dir in list_of_paths:\n", "#     if teams_dir.is_dir():\n", "#         DIR_179 = teams_dir\n", "#         found_dir = True\n", "\n", "# # Raise error if nothing works\n", "# if found_dir == False:\n", "#     raise ValueError(\"Please configure the Path to the 179d folder on your machine\")\n", "# else:\n", "#     print(\"179D Teams folder connected to {}\".format(DIR_179))\n", "\n", "try:\n", "    DIR_179 = Path(__file__).resolve().parent.parent\n", "except NameError:\n", "    # Fallback for interactive environments like Jupyter\n", "    DIR_179 = Path.cwd().parent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Path to web lookup file\n", "path_web_lookup = DIR_179 / \"files\" / \"web_lookups\" / \"179d_web_lookups.xlsx\"\n", "\n", "# # Path to comstock csv\n", "# path_comstock_csv = DIR_179 / \"files\" / \"comstock\" / \"baseline_metadata_and_annual_results.csv\"\n", "\n", "# Path to measure_spreadsheet\n", "spreadsheet_version = \"v16\"\n", "measure_spreadsheet_name = \"measure_spreadsheet_\"+spreadsheet_version+\".xlsx\"\n", "path_measure_spreadsheet = DIR_179 / \"files\" / \"measure_spreadsheets\" / measure_spreadsheet_name\n", "DIR_spreadsheets = DIR_179 / \"files\" / \"measure_spreadsheets\"\n", "\n", "assert path_web_lookup.is_file()\n", "# assert path_comstock_csv.is_file()\n", "assert path_measure_spreadsheet.is_file()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding hvac upgrade measures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set to True for adding comstock upgrade measures\n", "# set to False for only adding weblookup hvac variations (type 1 and up to 4)\n", "comstock_ee_upgrade_scenario = True\n", "\n", "# if above is True, make sure you list upgrade measures in this list below\n", "# key is a pretty name for the upgrade which will appear under in_hvac_system_type_proposed col in results csv\n", "# value is another dictionary\n", "# 'name' key should be the measure folder name\n", "# 'arguments' key should include exact argument names (as key) and input (as value) for the upgrade measure\n", "# if 'arguments' key is not defined, it will just use default arguments in the measure\n", "# `other_measure_arguments` can be used if some other measures/arguments need modification\n", "# if `other_measure_arguments` is not defined, nothing will happen\n", "hvac_upgrade_measures = {\n", "    \"VRF DOAS\": {\n", "        \"name\": \"upgrade_hvac_vrf_hr_doas\",\n", "        \"other_measure_arguments\": [\n", "            {\n", "                \"measure_name\": \"create_bar_from_building_type_ratios_comstock\",\n", "                \"argument_name\": \"total_bldg_floor_area\",\n", "                \"parameter_name_in_weblookup\": \"floor area\",\n", "                \"argument_value\": [0, 200000],  # [min, max] only for now\n", "            }\n", "        ],\n", "    },\n", "    \"MSHP DOAS\": {\n", "        \"name\": \"upgrade_hvac_doas_hp_minisplits\",\n", "        \"other_measure_arguments\": [\n", "            {\n", "                \"measure_name\": \"create_bar_from_building_type_ratios_comstock\",\n", "                \"argument_name\": \"total_bldg_floor_area\",\n", "                \"parameter_name_in_weblookup\": \"floor area\",\n", "                \"argument_value\": [0, 20000],  # [min, max] only for now\n", "            }\n", "        ],\n", "    },\n", "    \"HP RTU\": {\n", "        \"name\": \"upgrade_hvac_add_heat_pump_rtu\",\n", "    },\n", "    \"FCU ACC Blr DOAS\": {\n", "        \"name\": \"nze_hvac\",\n", "        \"arguments\": {\n", "            \"hvac_system_type\": \"DOAS with fan coil air-cooled chiller with boiler\",\n", "            \"remove_existing_hvac\": \"true\",\n", "        },\n", "    },\n", "    \"WSHP ClgTwr Blr\": {\n", "        \"name\": \"nze_hvac\",\n", "        \"arguments\": {\n", "            \"hvac_system_type\": \"Water source heat pumps cooling tower with boiler\",\n", "            \"remove_existing_hvac\": \"true\",\n", "        },\n", "    },\n", "    \"WSHP ClgTwr Blr DOAS\": {\n", "        \"name\": \"nze_hvac\",\n", "        \"arguments\": {\n", "            \"hvac_system_type\": \"DOAS with water source heat pumps cooling tower with boiler\",\n", "            \"remove_existing_hvac\": \"true\",\n", "        },\n", "    },\n", "    \"FCU ACC ASHP\": {\n", "        \"name\": \"nze_hvac\",\n", "        \"arguments\": {\n", "            \"hvac_system_type\": \"Fan coil air-cooled chiller with central air source heat pump\",\n", "            \"remove_existing_hvac\": \"true\",\n", "        },\n", "    },\n", "}\n", "\n", "# setting certain hvac type in create_typical measure for each upgrade measure\n", "# vrf doas measure grabs existing thermal zones so this is not really necessary\n", "# hp rtu measure grabs existing airloops so starting single zone hvac type seems safer than modifying the measure further\n", "base_hvac_type_for_hvac_upgrade_measure = {\n", "    \"VRF DOAS\": \"PSZ-AC with electric coil\",\n", "    \"MSHP DOAS\": \"PSZ-AC with electric coil\",\n", "    \"HP RTU\": \"PSZ-AC with electric coil\",\n", "    \"FCU ACC Blr DOAS\": \"PSZ-AC with gas coil\",\n", "    \"WSHP ClgTwr Blr\": \"PSZ-AC with gas coil\",\n", "    \"WSHP ClgTwr Blr DOAS\": \"PSZ-AC with gas coil\",\n", "    \"FCU ACC ASHP\": \"PSZ-AC with gas coil\",\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define output paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simulation batch assignment file name\n", "path_batch_config_file = (\n", "    DIR_179 / \"files\" / \"measure_spreadsheets\" / \"simulation_batch_assignment.csv\"\n", ")\n", "\n", "suffix = \"\"\n", "if comstock_ee_upgrade_scenario:\n", "    suffix = \"_w_hvacupgrades\"\n", "\n", "# Path to save parametric spreadsheet\n", "path_postfix_without_parameter_range_extension = \"parametric_spreadsheet_wo_param_ext\" + suffix\n", "path_postfix_with_parameter_range_extension = \"parametric_spreadsheet_w_param_ext\" + suffix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing configuration (read these carefully before processing the data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for parametric space creation\n", "cz_dictionary = {\n", "    \"1A\": \"USA_HI_Honolulu.Intl.AP.911820_TMY3.epw\",\n", "    \"2A\": \"USA_FL_Tampa.Intl.AP.722110_TMY3.epw\",\n", "    \"2B\": \"USA_AZ_Tucson.Intl.AP.722740_TMY3.epw\",\n", "    \"3A\": \"USA_GA_Atlanta-Hartsfield-Jackson.Intl.AP.722190_TMY3.epw\",\n", "    \"3B\": \"USA_TX_El.Paso.Intl.AP.722700_TMY3.epw\",\n", "    \"3C\": \"USA_CA_San.Jose-Mineta.Intl.AP.724945_TMY3.epw\",\n", "    \"4A\": \"USA_MD_Baltimore-Washington.Intl.AP.724060_TMY3.epw\",\n", "    \"4B\": \"USA_NM_Albuquerque.Intl.AP.723650_TMY3.epw\",\n", "    \"4C\": \"USA_WA_Seattle-Tacoma.Intl.AP.727930_TMY3.epw\",\n", "    \"5A\": \"USA_IL_Chicago-OHare.Intl.AP.725300_TMY3.epw\",\n", "    \"5B\": \"USA_UT_Salt.Lake.City.Intl.AP.725720_TMY3.epw\",\n", "    \"5C\": \"USA_WA_Port.Angeles-Fairchild.Intl.AP.727885_TMY3.epw\",\n", "    \"6A\": \"USA_MN_Rochester.Intl.AP.726440_TMY3.epw\",\n", "    \"6B\": \"USA_MT_Helena.Rgnl.AP.727720_TMY3.epw\",\n", "    \"7\": \"USA_MN_Duluth.Intl.AP.727450_TMY3.epw\",\n", "    \"8\": \"USA_AK_Fairbanks.Intl.AP.702610_TMY3.epw\",\n", "}\n", "parameter_sample_size = 200\n", "total_number_of_samples_per_batch_training = 1000\n", "total_number_of_samples_per_batch_testing = 500\n", "template = \"179D 90.1-2007\"  # for create_bar.. and create_typical.. measures\n", "template_fallback = \"90.1-2007\"\n", "story_multiplier = \"Basements Ground Mid Top\"  # for create_bar.. measure\n", "wall_construction_type = \"SteelFramed\"  # for create_typical.. measure\n", "add_exterior_lights = \"FALSE\"  # for create_typical.. measure\n", "onsite_parking_fraction = \"0\"  # for create_typical.. measure\n", "allow_reduction = \"TRUE\"  # for wall/roof insulation measures\n", "scenario_type = [\"proposed\", \"baseline\"]\n", "data_type = [\"training\", \"testing\"]\n", "\n", "# for parametric range extension\n", "extension_percent = 4  # in %\n", "convergence_study = False  # if this data processing for convergence study data set?\n", "\n", "# hvac system type grouping\n", "list_hvac_w_dx_cooling = [\n", "    \"PSZ-AC with electric coil\",\n", "    \"PSZ-AC with gas coil\",\n", "    \"PTAC with gas coil\",\n", "    \"PVAV with gas boiler reheat\",\n", "    \"PVAV with gas heat with electric reheat\",\n", "    \"PSZ-HP\",\n", "    \"PTHP\",\n", "    \"Residential AC with residential forced air furnace\",\n", "]\n", "list_hvac_w_gas_heating = [\n", "    \"PSZ-AC with gas coil\",\n", "    \"PTAC with gas coil\",\n", "    \"PVAV with gas heat with electric reheat\",\n", "    \"Residential AC with residential forced air furnace\",\n", "]\n", "list_hvac_w_hp_heating = [\"PSZ-HP\", \"PTHP\"]\n", "list_hvac_w_chiller = [\"VAV air-cooled chiller with gas boiler reheat\"]\n", "list_hvac_w_gas_boiler = [\"VAV air-cooled chiller with gas boiler reheat\", \"PVAV with gas boiler reheat\"]\n", "list_hvac_w_air_loop = [\n", "    \"PSZ-AC with electric coil\",\n", "    \"PSZ-AC with gas coil\",\n", "    \"PSZ-HP\",\n", "    \"PSZ-HP\",\n", "    \"PVAV with gas boiler reheat\",\n", "    \"PVAV with gas heat with electric reheat\",\n", "    \"VAV air-cooled chiller with gas boiler reheat\",\n", "]\n", "\n", "# hard-coding worst insulation values for wall and roof\n", "insulation_dictionary = {\n", "    \"1\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"2\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"3\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"4\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"5\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"6\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"7\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "    \"8\": {\n", "        \"Warehouse\": {\n", "            \"roof\": 7.3,\n", "            \"wall\": 5.9,\n", "        },\n", "        \"Others\": {\n", "            \"roof\": 19.7,\n", "            \"wall\": 13.4,\n", "        },\n", "    },\n", "}\n", "\n", "# hard-coding parameters to skip parameter min/max extension\n", "map_parameter_to_skip_extension = [\n", "    {\n", "        \"measure_name\": \"create_bar_from_building_type_ratios_comstock\",\n", "        \"argument_name\": \"total_bldg_floor_area\",\n", "    },\n", "    {\n", "        \"measure_name\": \"create_bar_from_building_type_ratios_comstock\",\n", "        \"argument_name\": \"wwr\",\n", "    },\n", "    {\n", "        \"measure_name\": \"set_water_heater_efficiency\",\n", "        \"argument_name\": \"performance_category_ratio\",\n", "    },\n", "    {\n", "        \"measure_name\": \"set_water_heater_efficiency\",\n", "        \"argument_name\": \"capacity_btu_per_hr_ratio\",\n", "    },\n", "    {\n", "        \"measure_name\": \"set_water_heater_efficiency\",\n", "        \"argument_name\": \"first_hour_rating_ratio\",\n", "    },\n", "    {\n", "        \"measure_name\": \"set_heating_cooling_cop_efficiency\",\n", "        \"argument_name\": \"efficiency\",\n", "    },\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Read parametric space data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the specific sheet of the Excel file\n", "df_parametric_space = pd.read_excel(path_web_lookup, sheet_name=\"parametric space\")\n", "df_parametric_space"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract key parameters for batch assigning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["map_parameter_space = {}\n", "\n", "# Extract building types (starting from the index after 'Continuous-Discrete')\n", "building_types = list(\n", "    df_parametric_space.columns[df_parametric_space.columns.tolist().index(\"Continuous-Discrete\") + 1 :]\n", ")\n", "print(\"### extracted building types = {}\".format(building_types))\n", "\n", "# map CZ for each building type\n", "for bld in building_types:\n", "    map_parameter_space[bld] = {}\n", "    list_cz = df_parametric_space[df_parametric_space[\"Name\"] == \"weather\"][bld].values[0].split(\", \")\n", "    map_parameter_space[bld][\"cz\"] = list_cz\n", "    print(\"### extracted climate zones for {} = {}\".format(bld, list_cz))\n", "\n", "# map hvac types for each building type\n", "for bld in building_types:\n", "    hvac_system_types = list(\n", "        df_parametric_space.loc[\n", "            (df_parametric_space[\"Name\"].str.startswith(\"type\"))\n", "            & (df_parametric_space[\"Name\"].str.contains(\" - name\"))\n", "            & (~df_parametric_space[bld].isnull()),\n", "            :,\n", "        ].Name.str.replace(\" - name\", \"\")\n", "    )\n", "    map_parameter_space[bld][\"hvac_type\"] = hvac_system_types\n", "    print(\"### extracted hvac types for {} = {}\".format(bld, hvac_system_types))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create batch configuration file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_combinations = []\n", "\n", "# Iterate over each building type and its climate zones\n", "for building_type in building_types:\n", "    # Get all variation of hvac types\n", "    hvac_type_variation = map_parameter_space[building_type][\"hvac_type\"]\n", "    if comstock_ee_upgrade_scenario:\n", "        for comstock_ugprade_measure_name in list(hvac_upgrade_measures.keys()):\n", "            hvac_type_variation.append(comstock_ugprade_measure_name)\n", "\n", "    # Generate all combinations for the current building type\n", "    combinations = list(\n", "        itertools.product(\n", "            [building_type],\n", "            hvac_type_variation,\n", "            map_parameter_space[building_type][\"cz\"],\n", "            scenario_type,\n", "            data_type,\n", "        )\n", "    )\n", "    # Extend the all_combinations list with the new combinations\n", "    all_combinations.extend(combinations)\n", "\n", "# Create a DataFrame from all_combinations\n", "df_batch_config = pd.DataFrame(\n", "    all_combinations, columns=[\"Building type\", \"HVAC system type\", \"Climate Zone\", \"Scenario type\", \"Data type\"]\n", ")\n", "\n", "# Reset the index to start with 58 as the Batch number\n", "df_batch_config.index = df_batch_config.index + 1\n", "\n", "df_batch_config.index.name = \"Batch number\"\n", "\n", "# Add Additional Columns\n", "df_batch_config[\"Algorithm\"] = \"LHS\"\n", "df_batch_config[\"Do you want to run this batch? [Y/N]\"] = np.nan\n", "\n", "# Adding the Seed column based on the condition specified\n", "df_batch_config[\"Seed\"] = df_batch_config[\"Data type\"].apply(lambda x: 179 if x == \"training\" else 901)\n", "\n", "df_batch_config.to_csv(path_batch_config_file)\n", "df_batch_config"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Selecting the batches to run in the batch configuration, df_batch_config."]}, {"cell_type": "markdown", "metadata": {}, "source": ["if you want to start off of where you left off before,\n", "- set `resume_runs_from_before` to `True`\n", "- specify the folder path that contains finished results csvs\n", "- it will do nothing if the path is not a real directory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["resume_runs_from_before = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if resume_runs_from_before == True:\n", "    # Define folder path that contains results csvs that have been created already\n", "    path_existing_run_folder = (\n", "        DIR_179 / \"data/3 if looking for 179D calculator data/Results_for_tests/241208_production_test/data/2nd\"\n", "    )\n", "else:\n", "    path_existing_run_folder = DIR_179 / \"dummy\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to run only certain runs/batches from runs/batches defined in `simulation_batch_assignment.csv`,\n", "- open the created `simulation_batch_assignment.csv` \n", "- add `Y` in the `Do you want to run this batch? [Y/N]` column in the file\n", "- you can leave the remaining rows in that column blank, no need to add N in the rows that you don't want\n", "- note that if this whole column is blank then all of the batches are included."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Re-read the simulation_batch_assignment.csv file\n", "df_batch_config = pd.read_csv(path_batch_config_file)\n", "\n", "# Reset index to Batch number\n", "df_batch_config.set_index(\"Batch number\", inplace=True)\n", "\n", "# Check if there is any selected batch. Batches can be selected only chosing Y, any other string is not read as a choice.\n", "if df_batch_config[\"Do you want to run this batch? [Y/N]\"].notnull().any():\n", "    print(\"### found {} batches from simulation_batch_assignment.csv file\".format(df_batch_config.shape[0]))\n", "\n", "    # Filter down the dataframe to the rows with 'Do you want to run this batch?[Y/N]' == 'Y'\n", "    df_batch_config = df_batch_config[df_batch_config[\"Do you want to run this batch? [Y/N]\"] == \"Y\"]\n", "\n", "    print(\n", "        \"### found {} batches from simulation_batch_assignment.csv file after filtering the selections\".format(\n", "            df_batch_config.shape[0]\n", "        )\n", "    )\n", "\n", "# Filter/remove batches based on existing batch numbers\n", "if path_existing_run_folder.is_dir():\n", "\n", "    list_csv_paths = glob.glob(str(path_existing_run_folder) + \"/batch*.csv\")\n", "    existing_batch_numbers = list(\n", "        pd.DataFrame(list_csv_paths).iloc[:, 0].str.extract(r\"batch(\\d+)_\").astype(int).iloc[:, 0]\n", "    )\n", "\n", "    print(\"### found {} results csvs from the specified run folder\".format(len(list_csv_paths)))\n", "    print(\n", "        \"### total number of runs to simulate in simulation_batch_assignment.csv = {} | before\".format(\n", "            df_batch_config.loc[df_batch_config[\"Do you want to run this batch? [Y/N]\"] == \"Y\", :].shape[0]\n", "        )\n", "    )\n", "\n", "    df_batch_config.loc[df_batch_config.index.isin(existing_batch_numbers), \"Do you want to run this batch? [Y/N]\"] = \"\"\n", "    df_batch_config = df_batch_config[df_batch_config[\"Do you want to run this batch? [Y/N]\"] == \"Y\"]\n", "    print(\n", "        \"### total number of runs to simulate in simulation_batch_assignment.csv = {} | after removing existing results csvs\".format(\n", "            df_batch_config.shape[0]\n", "        )\n", "    )\n", "\n", "else:\n", "\n", "    print(\n", "        \"### existing run folder not specified, thus, simulation_batch_assignment.csv remains with initial selections\"\n", "    )\n", "\n", "df_batch_config"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the parametric spreadsheet template"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["measure_list = []\n", "arguments_list = []\n", "value_list = []\n", "\n", "df_measure_spreadsheet = pd.read_excel(path_measure_spreadsheet)\n", "\n", "# Filter out lines without an order number\n", "df_measure_spreadsheet = df_measure_spreadsheet[df_measure_spreadsheet[\"Order\"].notna()]\n", "\n", "measure_list = []\n", "arguments_list = []\n", "value_list = []\n", "\n", "for measure in df_measure_spreadsheet[\"Measure_Name\"]:\n", "\n", "    arguments = df_measure_spreadsheet[df_measure_spreadsheet[\"Measure_Name\"] == measure][\"Measure_Arg_Val\"].values[0]\n", "    # Preprocess the string to replace Ruby booleans and hash rockets with Python-compatible syntax\n", "    arguments_preprocessed = (\n", "        arguments.replace(\"=>\", \": \").replace(\"'\", '\"').replace(\"true\", \"TRUE\").replace(\"false\", \"FALSE\")\n", "    )\n", "\n", "    # Use ast.literal_eval for safer evaluation\n", "    try:\n", "        arguments_dict = ast.literal_eval(arguments_preprocessed)\n", "    except ValueError as e:\n", "        print(f\"Error processing {measure}: {e}\")\n", "\n", "        break\n", "        continue\n", "\n", "    if measure != \"algorithm_setting\":\n", "        for k in arguments_dict.keys():\n", "            measure_list.append(measure)\n", "            arguments_list.append(k)\n", "            value_list.append(arguments_dict[k])\n", "\n", "# Algorithm Settings\n", "measure_list.append(\"algorithm_setting\")\n", "arguments_list.append(\"seed\")\n", "value_list.append(\"179\")  # temporary\n", "measure_list.append(\"algorithm_setting\")\n", "arguments_list.append(\"number_of_samples\")\n", "value_list.append(str(total_number_of_samples_per_batch_training))\n", "\n", "df_parametric_spreadsheet = pd.DataFrame(\n", "    {\"Measure Name\": measure_list, \"Argument Name\": arguments_list, \"Default Value\": value_list}\n", ")\n", "\n", "df_parametric_spreadsheet = df_parametric_spreadsheet.replace(\"NA\", np.nan)\n", "df_parametric_spreadsheet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Iteratively read the parameters and fill up the spreadsheet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#######################################################################################################\n", "# populating continuous samples from web lookup spreadsheet\n", "#######################################################################################################\n", "def populate_continuous_samples(\n", "    df_parametric_spreadsheet,\n", "    df_parametric_space,\n", "    parameter_name,\n", "    batch_number,\n", "    measure_name,\n", "    argument_name,\n", "    building_type,\n", "    step,\n", "    sample=None,\n", "):\n", "\n", "    # decide on data type\n", "    dtype = (\n", "        \"stats\"\n", "        if df_parametric_space[df_parametric_space[\"Name\"] == \"{} max\".format(parameter_name)][\n", "            \"Continuous-Discrete\"\n", "        ].values[0]\n", "        == \"continuous\"\n", "        else \"actuals\"\n", "    )\n", "\n", "    if dtype == \"stats\":\n", "\n", "        # extract min and max values\n", "        min_ = float(\n", "            df_parametric_space[df_parametric_space[\"Name\"] == \"{} min\".format(parameter_name)]\n", "            .copy()[building_type]\n", "            .values[0]\n", "        )\n", "        max_ = float(\n", "            df_parametric_space[df_parametric_space[\"Name\"] == \"{} max\".format(parameter_name)]\n", "            .copy()[building_type]\n", "            .values[0]\n", "        )\n", "\n", "        # override if (restricted) samples are provided directly\n", "        if not sample == None:\n", "            print(\"--- overriding samples provided: sample = {}\".format(sample))\n", "\n", "            min_new = sample[0]\n", "            max_new = sample[1]\n", "            print(\"--- min_original = {} | min_new = {}\".format(min_, min_new))\n", "            print(\"--- max_original = {} | max_new = {}\".format(max_, max_new))\n", "\n", "            if max_new <= min_:\n", "                print(\n", "                    f\"--- not overriding: new values outside of orignal range: max_new = {max_new} < min_original = {min_}\"\n", "                )\n", "            elif min_new >= max_:\n", "                print(\n", "                    f\"--- not overriding: new values outside of orignal range: min_new = {min_new} < max_original = {max_}\"\n", "                )\n", "            else:\n", "                if min_new > min_:\n", "                    min_ = min_new\n", "                if max_new < max_:\n", "                    max_ = max_new\n", "                print(\"--- overriding samples provided: min_final = {} | max_final = {}\".format(min_, max_))\n", "\n", "        if np.isnan(min_) | np.isnan(max_):\n", "            print(f\"skipping population for batch {batch_number}: '{parameter_name}' min = {min_} | max = {max_}\")\n", "        else:\n", "            # assign samples\n", "            df_parametric_spreadsheet.loc[\n", "                (df_parametric_spreadsheet[\"Measure Name\"] == measure_name)\n", "                & (df_parametric_spreadsheet[\"Argument Name\"] == argument_name),\n", "                f\"Samples_as_<PERSON><PERSON><PERSON>_Batch{str(int(batch_number))}\",\n", "            ] = f\"[{str(min_)}, {str(max_)}, {step}]\"\n", "            df_parametric_spreadsheet.loc[\n", "                (df_parametric_spreadsheet[\"Measure Name\"] == measure_name)\n", "                & (df_parametric_spreadsheet[\"Argument Name\"] == argument_name),\n", "                f\"Sample_Type_Batch{str(int(batch_number))}\",\n", "            ] = dtype\n", "\n", "    else:\n", "        raise \"cannot implement statistical sample information when data type of {} is {}\".format(parameter_name, dtype)\n", "\n", "    return df_parametric_spreadsheet.copy()\n", "\n", "\n", "#######################################################################################################\n", "# populating discrete samples from either web lookup spreadsheet or hard-coded samples in this notebook\n", "#######################################################################################################\n", "def populate_discrete_samples(\n", "    df_parametric_spreadsheet,\n", "    df_parametric_space,\n", "    df_batch_config,\n", "    batch_number,\n", "    measure_name,\n", "    argument_name,\n", "    from_weblookup,\n", "    parameter_name=None,\n", "    sample=None,\n", "):\n", "\n", "    building_type = df_batch_config[df_batch_config.index == batch_number][\"Building type\"].values[0]\n", "\n", "    if from_weblookup:\n", "\n", "        samples_raw = df_parametric_space[df_parametric_space[\"Name\"] == parameter_name][building_type].values[0]\n", "        if \",\" not in str(samples_raw):\n", "            samples_processed = str(samples_raw)\n", "        else:\n", "            samples_processed = \"[\" + str(samples_raw) + \"]\"\n", "\n", "    else:\n", "        samples_processed = sample\n", "\n", "    df_parametric_spreadsheet.loc[\n", "        (df_parametric_spreadsheet[\"Measure Name\"] == measure_name)\n", "        & (df_parametric_spreadsheet[\"Argument Name\"] == argument_name),\n", "        f\"Samples_as_<PERSON><PERSON><PERSON>_Batch{str(int(batch_number))}\",\n", "    ] = samples_processed\n", "\n", "    return df_parametric_spreadsheet.copy()\n", "\n", "\n", "#######################################################################################################\n", "# populate samples using two methods above\n", "#######################################################################################################\n", "def populate_samples(\n", "    df_parametric_spreadsheet,\n", "    df_parametric_space,\n", "    df_batch_config,\n", "    batch_number,\n", "    measure_name,\n", "    argument_name,\n", "    from_weblookup,\n", "    step=None,\n", "    parameter_name=None,\n", "    sample=None,\n", "):\n", "    # get building type\n", "    building_type = df_batch_config[df_batch_config.index == batch_number][\"Building type\"].values[0]\n", "\n", "    print(\"### ---------------------------------------------\")\n", "    print(\"### populate_samples\")\n", "    print(\"--- measure_name = {}\".format(measure_name))\n", "    print(\"--- argument_name = {}\".format(argument_name))\n", "    print(\"--- parameter_name = {}\".format(parameter_name))\n", "    print(\"--- sample = {}\".format(sample))\n", "\n", "    # populate samples\n", "    sample_type = \"\"\n", "    if not from_weblookup:\n", "\n", "        print(\"--- resource -> not from weblookup\")\n", "\n", "        sample_type = \"discrete\"\n", "\n", "        print(\"--- sample_type = {}\".format(sample_type))\n", "        print(\"--- populate via populate_discrete_samples\")\n", "\n", "        df_parametric_spreadsheet = populate_discrete_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            measure_name,\n", "            argument_name,\n", "            from_weblookup,\n", "            sample=sample,\n", "        )\n", "\n", "    else:\n", "\n", "        print(\"--- resource -> from weblookup\")\n", "\n", "        sample_types = df_parametric_space.loc[\n", "            df_parametric_space[\"Name\"].str.contains(parameter_name + \"*\", regex=True), \"Continuous-Discrete\"\n", "        ].drop_duplicates()\n", "\n", "        if len(sample_types) == 1:\n", "            sample_type = sample_types.iloc[0]\n", "        else:\n", "            raise f\"sample type does not match for this argument f{argument_name}: sample_types = {sample_types}\"\n", "\n", "        print(\"--- sample_type = {}\".format(sample_type))\n", "\n", "        if sample_type == \"continuous\":\n", "\n", "            print(\"--- populate via populate_continuous_samples\")\n", "\n", "            df_parametric_spreadsheet = populate_continuous_samples(\n", "                df_parametric_spreadsheet,\n", "                df_parametric_space,\n", "                parameter_name,\n", "                batch_number,\n", "                measure_name,\n", "                argument_name,\n", "                building_type,\n", "                step,\n", "                sample,\n", "            )\n", "\n", "        elif sample_type == \"discrete\":\n", "\n", "            print(\"--- populate via populate_discrete_samples\")\n", "\n", "            df_parametric_spreadsheet = populate_discrete_samples(\n", "                df_parametric_spreadsheet,\n", "                df_parametric_space,\n", "                df_batch_config,\n", "                batch_number,\n", "                measure_name,\n", "                argument_name,\n", "                from_weblookup,\n", "                parameter_name=parameter_name,\n", "            )\n", "\n", "        else:\n", "            raise f\"data sample type is not 'continuos' nor 'discrete': sample_type = {sample_type}\"\n", "\n", "    return df_parametric_spreadsheet.copy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for batch_number in df_batch_config.index:\n", "\n", "    # ---------------------------------------------------------------------\n", "    # Extract batch information from batch configuration file\n", "    # ---------------------------------------------------------------------\n", "    building_type = df_batch_config[df_batch_config.index == batch_number][\"Building type\"].values[0]\n", "    hvac_system_type = df_batch_config[df_batch_config.index == batch_number][\"HVAC system type\"].values[0]\n", "    cz = str(df_batch_config[df_batch_config.index == batch_number][\"Climate Zone\"].values[0])\n", "    scenario_type = df_batch_config[df_batch_config.index == batch_number][\"Scenario type\"].values[0]\n", "    data_type = df_batch_config[df_batch_config.index == batch_number][\"Data type\"].values[0]\n", "    seed = df_batch_config[df_batch_config.index == batch_number][\"Seed\"].values[0]\n", "    if hvac_system_type in list(hvac_upgrade_measures.keys()):\n", "        hvac_system_type_name = hvac_upgrade_measures[hvac_system_type][\"name\"]\n", "    else:\n", "        hvac_system_type_name = df_parametric_space.loc[\n", "            df_parametric_space[\"Name\"].str.contains(hvac_system_type + \" - name\"), :\n", "        ][building_type].iloc[0]\n", "\n", "    print(\"### ===========================================================================\")\n", "    print(\"### processing\")\n", "    print(\"### batch_number = {}\".format(batch_number))\n", "    print(\"### building_type = {}\".format(building_type))\n", "    print(\"### hvac_system_type = {}\".format(hvac_system_type))\n", "    print(\"### hvac_system_type_name = {}\".format(hvac_system_type_name))\n", "    print(\"### cz = {}\".format(cz))\n", "    # ---------------------------------------------------------------------\n", "    # Populate empty columns for a batch\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet[f\"Sam<PERSON>_as_<PERSON><PERSON><PERSON>_Batch{str(int(batch_number))}\"] = np.nan\n", "    df_parametric_spreadsheet[f\"Sample_Type_Batch{str(int(batch_number))}\"] = np.nan\n", "\n", "    # ---------------------------------------------------------------------\n", "    # Convert column data type to string/object\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet[f\"<PERSON><PERSON>_as_<PERSON><PERSON><PERSON>_Batch{str(int(batch_number))}\"] = df_parametric_spreadsheet[\n", "        f\"Samples_as_<PERSON><PERSON><PERSON>_Batch{str(int(batch_number))}\"\n", "    ].astype(\"object\")\n", "    df_parametric_spreadsheet[f\"Sample_Type_Batch{str(int(batch_number))}\"] = df_parametric_spreadsheet[\n", "        f\"Sample_Type_Batch{str(int(batch_number))}\"\n", "    ].astype(\"object\")\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency: DX cooling\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_dx_cooling:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"set_heating_cooling_cop_efficiency\",\n", "            \"cooling_cop\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=f\"{hvac_system_type} - cooling dx coil cop\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency: DX heating\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_hp_heating:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"set_heating_cooling_cop_efficiency\",\n", "            \"heating_cop\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=f\"{hvac_system_type} - heating dx coil cop\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency: gas heating\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_gas_heating:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"set_heating_cooling_cop_efficiency\",\n", "            \"efficiency\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=f\"{hvac_system_type} - heating gas coil efficiency\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency: gas boiler\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_gas_boiler:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"set_heating_cooling_cop_efficiency\",\n", "            \"boiler_efficiency\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=f\"{hvac_system_type} - boiler efficiency\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency: air-cooled chiller\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_chiller:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"set_heating_cooling_cop_efficiency\",\n", "            \"chiller_cop\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=f\"{hvac_system_type} - chiller cop\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # total_bldg_floor_area\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"total_bldg_floor_area\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"floor area\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # ns_to_ew_ratio\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"ns_to_ew_ratio\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"aspect ratio\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # wwr\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"wwr\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"wwr\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # building_rotation\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"building_rotation\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"building rotation\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # r_value roof\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"IncreaseInsulationRValueForRoofs\",\n", "        \"r_value\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"insulation roof\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # r_value wall\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"IncreaseInsulationRValueForExteriorWalls\",\n", "        \"r_value\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"insulation wall\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # u_value_ip window\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"replace_baseline_windows_Rvalues\",\n", "        \"u_value_ip\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"window u value\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # shgc window\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"replace_baseline_windows_Rvalues\",\n", "        \"shgc\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"window shgc\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # lpd\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"SetLightingLoadsByLPD\",\n", "        \"lpd\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"lpd\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # weather File\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"ChangeBuildingLocation_179d_gem\",\n", "        \"weather_file_name\",\n", "        False,\n", "        sample=cz_dictionary[cz],\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # bldg_type_a\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"bldg_type_a\",\n", "        False,\n", "        sample=building_type,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # num_stories_above_grade\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"num_stories_above_grade\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"number of floors\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # story_multiplier\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"story_multiplier\",\n", "        False,\n", "        sample=story_multiplier,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # template\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"template\",\n", "        False,\n", "        sample=template_fallback,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # template\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_typical_building_from_model_comstock\",\n", "        \"template\",\n", "        False,\n", "        sample=template,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # template\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"baseline\":\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"create_179d_gem_baseline_building\",\n", "            \"standard\",\n", "            False,\n", "            sample=template,\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # template\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"proposed\":\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"179d_gem_baseline_hvac_control\",\n", "            \"standard\",\n", "            False,\n", "            sample=template,\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # system_type\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type in list(hvac_upgrade_measures.keys()):\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"create_typical_building_from_model_comstock\",\n", "            \"system_type\",\n", "            False,\n", "            sample=base_hvac_type_for_hvac_upgrade_measure[hvac_system_type],\n", "        )\n", "    else:\n", "        system_type_name = str(\n", "            df_parametric_space[df_parametric_space[\"Name\"] == f\"{hvac_system_type} - name\"][building_type].values[0]\n", "        )\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"create_typical_building_from_model_comstock\",\n", "            \"system_type\",\n", "            False,\n", "            sample=system_type_name,\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # swh_src\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_typical_building_from_model_comstock\",\n", "        \"swh_src\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"water heater fuel type\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # make_mid_story_surfaces_adiabatic\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_bar_from_building_type_ratios_comstock\",\n", "        \"make_mid_story_surfaces_adiabatic\",\n", "        False,\n", "        sample=\"TRUE\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # wall_construction_type\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_typical_building_from_model_comstock\",\n", "        \"wall_construction_type\",\n", "        False,\n", "        sample=wall_construction_type,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # add_exterior_lights\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_typical_building_from_model_comstock\",\n", "        \"add_exterior_lights\",\n", "        False,\n", "        sample=add_exterior_lights,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # onsite_parking_fraction\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_typical_building_from_model_comstock\",\n", "        \"onsite_parking_fraction\",\n", "        False,\n", "        sample=onsite_parking_fraction,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # capacity_btu_per_hr_ratio\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"set_water_heater_efficiency\",\n", "        \"capacity_btu_per_hr_ratio\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"water heater heater capacity ratio\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # first_hour_rating_ratio\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"set_water_heater_efficiency\",\n", "        \"first_hour_rating_ratio\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"water heater first hour rating ratio\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # performance_category_ratio\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"set_water_heater_efficiency\",\n", "        \"performance_category_ratio\",\n", "        True,\n", "        step=str(parameter_sample_size),\n", "        parameter_name=\"water heater performance category ratio\",\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # IncreaseInsulationRValueForRoofs\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"IncreaseInsulationRValueForRoofs\",\n", "        \"allow_reduction\",\n", "        False,\n", "        sample=allow_reduction,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # IncreaseInsulationRValueForExteriorWalls\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"IncreaseInsulationRValueForExteriorWalls\",\n", "        \"allow_reduction\",\n", "        False,\n", "        sample=allow_reduction,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # 179d_gem_baseline_hvac_control\n", "    # ---------------------------------------------------------------------\n", "    if (scenario_type == \"proposed\") & (hvac_system_type not in list(hvac_upgrade_measures.keys())):\n", "        skip_value = \"FALSE\"\n", "    elif (scenario_type == \"baseline\") | (hvac_system_type in list(hvac_upgrade_measures.keys())):\n", "        skip_value = \"TRUE\"\n", "    else:\n", "        raise ValueError(\n", "            f\"if condition does not fall within the two cases above: scenario_type = {scenario_type} | hvac_system_type = {hvac_system_type} | hvac_upgrade_measures = {list(hvac_upgrade_measures.keys())}\"\n", "        )\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"179d_gem_baseline_hvac_control\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # set_heating_cooling_cop_efficiency\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type in list(hvac_upgrade_measures.keys()):\n", "        skip_value = \"TRUE\"\n", "    else:\n", "        skip_value = \"FALSE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"set_heating_cooling_cop_efficiency\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # based on col name batch number, override swh_patch measure SKIP parameter\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"baseline\":\n", "        skip_value = \"FALSE\"\n", "    elif scenario_type == \"proposed\":\n", "        skip_value = \"TRUE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"swh_patch\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # based on col name batch number, override measure application\n", "    # ---------------------------------------------------------------------\n", "    if (scenario_type == \"baseline\") & (building_type == \"RetailStripmall\"):\n", "        skip_value = \"FALSE\"\n", "    else:\n", "        skip_value = \"TRUE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"remove_additional_lighting\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # create_179d_gem_baseline_building\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"baseline\":\n", "        skip_value = \"FALSE\"\n", "    elif scenario_type == \"proposed\":\n", "        skip_value = \"TRUE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"create_179d_gem_baseline_building\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # hardsize_model\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"baseline\":\n", "        skip_value = \"FALSE\"\n", "    elif scenario_type == \"proposed\":\n", "        skip_value = \"FALSE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"hardsize_model\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # hardsize_model_copy\n", "    # ---------------------------------------------------------------------\n", "    if scenario_type == \"baseline\":\n", "        skip_value = \"FALSE\"\n", "    elif scenario_type == \"proposed\":\n", "        skip_value = \"TRUE\"\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"hardsize_model_copy\",\n", "        \"__SKIP__\",\n", "        False,\n", "        sample=skip_value,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # algorithm settings\n", "    # ---------------------------------------------------------------------\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"algorithm_setting\",\n", "        \"seed\",\n", "        False,\n", "        sample=seed,\n", "    )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # total number of runs in each batch for training data\n", "    # ---------------------------------------------------------------------\n", "    if data_type == \"training\":\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"algorithm_setting\",\n", "            \"number_of_samples\",\n", "            False,\n", "            sample=total_number_of_samples_per_batch_training,\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # total number of runs in each batch for testing data\n", "    # ---------------------------------------------------------------------\n", "    if data_type == \"testing\":\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"algorithm_setting\",\n", "            \"number_of_samples\",\n", "            False,\n", "            sample=total_number_of_samples_per_batch_testing,\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # proper skipping of comstock upgrade measures\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type in list(hvac_upgrade_measures.keys()):\n", "        # don't skip hvac upgrade measure\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            hvac_system_type_name,\n", "            \"__SKIP__\",\n", "            False,\n", "            sample=\"FALSE\",\n", "        )\n", "        # assign specific argument if necessary for hvac upgrade measure\n", "        if \"arguments\" in hvac_upgrade_measures[hvac_system_type]:\n", "            print(\"--- replacing measure argument for HVAC upgrade measure: {}\".format(hvac_system_type_name))\n", "            for arg in list(hvac_upgrade_measures[hvac_system_type][\"arguments\"].keys()):\n", "                print(\n", "                    \"*** argument = {} | value = {}\".format(\n", "                        arg, hvac_upgrade_measures[hvac_system_type][\"arguments\"][arg]\n", "                    )\n", "                )\n", "                df_parametric_spreadsheet = populate_samples(\n", "                    df_parametric_spreadsheet,\n", "                    df_parametric_space,\n", "                    df_batch_config,\n", "                    batch_number,\n", "                    hvac_system_type_name,\n", "                    arg,\n", "                    False,\n", "                    sample=hvac_upgrade_measures[hvac_system_type][\"arguments\"][arg],\n", "                )\n", "        # assign specific argument if necessary for other measures\n", "        if \"other_measure_arguments\" in hvac_upgrade_measures[hvac_system_type]:\n", "            print(\"--- replacing arguments in other measures\")\n", "            for entry in hvac_upgrade_measures[hvac_system_type][\"other_measure_arguments\"]:\n", "                print(\n", "                    \"--- measure = {} | argument = {} | value = {}\".format(\n", "                        entry[\"measure_name\"], entry[\"argument_name\"], entry[\"argument_value\"]\n", "                    )\n", "                )\n", "                df_parametric_spreadsheet = populate_samples(\n", "                    df_parametric_spreadsheet,\n", "                    df_parametric_space,\n", "                    df_batch_config,\n", "                    batch_number,\n", "                    entry[\"measure_name\"],\n", "                    entry[\"argument_name\"],\n", "                    True,\n", "                    step=str(parameter_sample_size),\n", "                    parameter_name=entry[\"parameter_name_in_weblookup\"],\n", "                    sample=entry[\"argument_value\"],\n", "                )\n", "\n", "        # assign hvac type name properly in reporting supporting measure\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"reporting_179_d_support\",\n", "            \"hvac_type_from_upgrade\",\n", "            False,\n", "            sample=\"TRUE\",\n", "        )\n", "        # skip all the other hvac upgrade measures\n", "        for upgrade_measure_name in [item[\"name\"] for item in hvac_upgrade_measures.values()]:\n", "            if upgrade_measure_name != hvac_system_type_name:\n", "                df_parametric_spreadsheet = populate_samples(\n", "                    df_parametric_spreadsheet,\n", "                    df_parametric_space,\n", "                    df_batch_config,\n", "                    batch_number,\n", "                    upgrade_measure_name,\n", "                    \"__SKIP__\",\n", "                    False,\n", "                    sample=\"TRUE\",\n", "                )\n", "    else:\n", "        # skip all hvac upgrade measures\n", "        for upgrade_measure_name in [item[\"name\"] for item in hvac_upgrade_measures.values()]:\n", "            df_parametric_spreadsheet = populate_samples(\n", "                df_parametric_spreadsheet,\n", "                df_parametric_space,\n", "                df_batch_config,\n", "                batch_number,\n", "                upgrade_measure_name,\n", "                \"__SKIP__\",\n", "                False,\n", "                sample=\"TRUE\",\n", "            )\n", "        # assign hvac type name properly in reporting supporting measure\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"reporting_179_d_support\",\n", "            \"hvac_type_from_upgrade\",\n", "            False,\n", "            sample=\"FALSE\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # erv\n", "    # ---------------------------------------------------------------------\n", "    if hvac_system_type_name in list_hvac_w_air_loop:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"upgrade_hvac_exhaust_air_energy_or_heat_recovery\",\n", "            \"__SKIP__\",\n", "            True,\n", "            step=str(parameter_sample_size),\n", "            parameter_name=\"skip for heat or energy recovery ventilator\",\n", "        )\n", "    else:\n", "        df_parametric_spreadsheet = populate_samples(\n", "            df_parametric_spreadsheet,\n", "            df_parametric_space,\n", "            df_batch_config,\n", "            batch_number,\n", "            \"upgrade_hvac_exhaust_air_energy_or_heat_recovery\",\n", "            \"__SKIP__\",\n", "            False,\n", "            sample=\"TRUE\",\n", "        )\n", "\n", "    # ---------------------------------------------------------------------\n", "    # diagnostics output\n", "    # ---------------------------------------------------------------------\n", "    # assign hvac type name properly in reporting supporting measure\n", "    df_parametric_spreadsheet = populate_samples(\n", "        df_parametric_spreadsheet,\n", "        df_parametric_space,\n", "        df_batch_config,\n", "        batch_number,\n", "        \"reporting_179_d\",\n", "        \"add_warning_severe_counts\",\n", "        False,\n", "        sample=\"TRUE\",\n", "    )\n", "\n", "# Create a directory if it doesn't exist\n", "directory_lvl1 = Path(DIR_spreadsheets / path_postfix_without_parameter_range_extension)\n", "if directory_lvl1.exists():\n", "    print(f\"Directory '{directory_lvl1}' already exists.\")\n", "else:\n", "    os.makedirs(directory_lvl1)\n", "    print(f\"Directory '{directory_lvl1}' created successfully.\")\n", "\n", "directory_lvl1_file_path = directory_lvl1 / \"parametric_spreadsheet.csv\"\n", "df_parametric_spreadsheet.to_csv(directory_lvl1_file_path, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cutomize ranges for certain parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# file to read\n", "print(directory_lvl1_file_path)\n", "\n", "# Read the CSV file into a pandas DataFrame with all columns as strings\n", "df = pd.read_csv(directory_lvl1_file_path, dtype=str, na_values=\"\", keep_default_na=False)\n", "# Get the column names containing the word \"Bat<PERSON>\"\n", "batch_columns = [col for col in df.columns if \"Batch\" in col]\n", "\n", "# QAQC one more\n", "for batch_column in batch_columns:\n", "    # Extract the number after \"<PERSON><PERSON>\" using regular expressions\n", "    batch_number = int(batch_column.split(\"Batch\")[1].split(\"_\")[0])\n", "    # Find the corresponding row in 'Batches.csv' based on the batch number\n", "    batch_info = df_batch_config[df_batch_config.index == batch_number]\n", "\n", "    if batch_info.empty:\n", "        raise Exception(\"cannot find batch number ({}) from the source file\".format(batch_number))\n", "    else:\n", "\n", "        if \"Samples_as_Array_\" in batch_column:\n", "\n", "            # Check if the batch number exists in 'Batches.csv'\n", "            # Extract the 'Scenario type'\n", "            building_type = batch_info[\"Building type\"].values[0]\n", "            scenario_type = batch_info[\"Scenario type\"].values[0]\n", "            data_type = batch_info[\"Data type\"].values[0]\n", "            climate_zone = batch_info[\"Climate Zone\"].values[0]\n", "            climate_zone_num = \"\".join([char for char in climate_zone if char.isdigit()])\n", "            hvac_system_type = batch_info[\"HVAC system type\"].values[0]\n", "            if hvac_system_type in list(hvac_upgrade_measures.keys()):\n", "                hvac_system_type_name = hvac_system_type\n", "            else:\n", "                hvac_system_type_name = df_parametric_space.loc[\n", "                    df_parametric_space[\"Name\"].str.contains(hvac_system_type + \" - name\"), :\n", "                ][building_type].iloc[0]\n", "\n", "            print(\"--------------------------------------------------------------\")\n", "            print(\"--- checking batch_number = {}\".format(batch_number))\n", "            print(\"--- building_type = {}\".format(building_type))\n", "            print(\"--- scenario_type = {}\".format(scenario_type))\n", "            print(\"--- data_type = {}\".format(data_type))\n", "            print(\"--- climate_zone = {}\".format(climate_zone))\n", "            print(\"--- climate_zone_num = {}\".format(climate_zone_num))\n", "            print(\"--- hvac_system_type = {}\".format(hvac_system_type))\n", "            print(\"--- hvac_system_type_name = {}\".format(hvac_system_type_name))\n", "\n", "            ##################################################################################\n", "            # apply worst insulation values differently depending on climate zone\n", "            new_value = []\n", "            name_measure = \"IncreaseInsulationRValueForRoofs\"\n", "            name_argument = \"r_value\"\n", "            # Read orginal value\n", "            original_value = ast.literal_eval(\n", "                df.loc[\n", "                    (df[\"Measure Name\"] == name_measure) & (df[\"Argument Name\"] == name_argument), batch_column\n", "                ].iloc[0]\n", "            )\n", "            # If it is a list, make a copy and assign it to new_value\n", "            if isinstance(original_value, list):\n", "                new_value = original_value.copy()\n", "            else:\n", "                new_value = original_value\n", "\n", "            # update new value\n", "            if building_type in insulation_dictionary[climate_zone_num]:\n", "                new_value[0] = insulation_dictionary[climate_zone_num][building_type][\"roof\"]\n", "            else:\n", "                new_value[0] = insulation_dictionary[climate_zone_num][\"Others\"][\"roof\"]\n", "\n", "            print(\"--- *********************\")\n", "            print(\"--- original_value = {}\".format(str(original_value)))\n", "            print(\"--- new_value = {}\".format(str(new_value)))\n", "            if str(original_value) != str(new_value):\n", "                print(\n", "                    \"--- overriding argument ({}) in measure ({}) from {} to {}\".format(\n", "                        name_argument, name_measure, str(original_value), str(new_value)\n", "                    )\n", "                )\n", "                df.loc[(df[\"Measure Name\"] == name_measure) & (df[\"Argument Name\"] == name_argument), batch_column] = (\n", "                    str(new_value)\n", "                )\n", "\n", "            ##################################################################################\n", "            # apply worst insulation values differently depending on climate zone\n", "            new_value = []\n", "            name_measure = \"IncreaseInsulationRValueForExteriorWalls\"\n", "            name_argument = \"r_value\"\n", "            # Read orginal value\n", "            original_value = ast.literal_eval(\n", "                df.loc[\n", "                    (df[\"Measure Name\"] == name_measure) & (df[\"Argument Name\"] == name_argument), batch_column\n", "                ].iloc[0]\n", "            )\n", "            # If it is a list, make a copy and assign it to new_value\n", "            if isinstance(original_value, list):\n", "                new_value = original_value.copy()\n", "            else:\n", "                new_value = original_value\n", "\n", "            # update new value\n", "            if building_type in insulation_dictionary[climate_zone_num]:\n", "                new_value[0] = insulation_dictionary[climate_zone_num][building_type][\"wall\"]\n", "            else:\n", "                new_value[0] = insulation_dictionary[climate_zone_num][\"Others\"][\"wall\"]\n", "\n", "            print(\"--- *********************\")\n", "            print(\"--- original_value = {}\".format(str(original_value)))\n", "            print(\"--- new_value = {}\".format(str(new_value)))\n", "            if str(original_value) != str(new_value):\n", "                print(\n", "                    \"--- overriding argument ({}) in measure ({}) from {} to {}\".format(\n", "                        name_argument, name_measure, str(original_value), str(new_value)\n", "                    )\n", "                )\n", "                df.loc[(df[\"Measure Name\"] == name_measure) & (df[\"Argument Name\"] == name_argument), batch_column] = (\n", "                    str(new_value)\n", "                )\n", "\n", "df.to_csv(directory_lvl1_file_path, index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Apply parameter range extension"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to rename batch columns based on configuration\n", "def rename_batch_columns(df, batch_columns, df_batch_config, convergence_study, extension_percent):\n", "    for batch_column in batch_columns:\n", "        batch_number = int(\"\".join(filter(str.isdigit, batch_column)))\n", "        batch_info = df_batch_config[df_batch_config.index == batch_number]\n", "\n", "        if batch_info.empty:\n", "            raise Exception(f\"Cannot find batch number ({batch_number}) from the source file\")\n", "\n", "        scenario_type = batch_info[\"Scenario type\"].values[0]\n", "        data_type = batch_info[\"Data type\"].values[0]\n", "\n", "        if scenario_type in batch_column and data_type in batch_column:\n", "            continue\n", "\n", "        new_column_name = (\n", "            f\"{batch_column}_{scenario_type}_{data_type}_{extension_percent}\"\n", "            if convergence_study\n", "            else f\"{batch_column}_{scenario_type}_{data_type}\"\n", "        )\n", "        df.rename(columns={batch_column: new_column_name}, inplace=True)\n", "\n", "\n", "def modify_numbers(cell, df, col, index_row, list_index_to_skip=None, extension_percent=4):\n", "    try:\n", "        cell_str = str(cell)\n", "        if \"[\" in cell_str and \"]\" in cell_str:\n", "            print(f\"--- Found a list of samples: {cell_str}\")\n", "\n", "            # Extract and evaluate the list of numbers\n", "            numbers_list = ast.literal_eval(cell_str.split(\"[\")[1].split(\"]\")[0])\n", "            if not all(isinstance(num, (int, float)) for num in numbers_list):\n", "                return cell\n", "\n", "            # Get the column of the adjacent cell\n", "            column_name = df.columns[df.columns.get_loc(col) + 1]\n", "            value_type = df.at[index_row, column_name]\n", "            measure_name = df.at[index_row, \"Measure Name\"]\n", "            argument_name = df.at[index_row, \"Argument Name\"]\n", "\n", "            print(f\"    index_row = {index_row} | numbers_list = {numbers_list} | value_type = {value_type}\")\n", "            print(f\"    measure_name = {measure_name} | argument_name = {argument_name}\")\n", "\n", "            # Check if the index should be skipped\n", "            if index_row in list(list_index_to_skip):\n", "                skip_info = list_index_to_skip[index_row]\n", "                print(\n", "                    f\"    Skipping index {index_row}: Excluded parameters | {skip_info['measure_name']} | {skip_info['argument_name']}\"\n", "                )\n", "                return cell\n", "\n", "            # Modify numbers based on the condition\n", "            if value_type == \"stats\":\n", "                modified_numbers = [\n", "                    round(numbers_list[0] * (1 - extension_percent / 100), 3),\n", "                    round(numbers_list[1] * (1 + extension_percent / 100), 3),\n", "                ] + list(numbers_list[2:])\n", "\n", "                print(f\"    Modified numbers: {modified_numbers}\")\n", "                return str(modified_numbers)\n", "            else:\n", "                print(f\"    Skipping index {index_row} with sample {cell_str}: Data type not continuous\")\n", "\n", "    except (ValueError, SyntaxError, IndexError, KeyError) as e:\n", "        print(f\"    Skipping cell {cell}: {e}\")\n", "\n", "    return cell\n", "\n", "\n", "# Validate file existence\n", "if not directory_lvl1_file_path.is_file():\n", "    raise ValueError(\"Missing the CSV file\")\n", "print(directory_lvl1_file_path)\n", "\n", "# Load data from CSV file\n", "df = pd.read_csv(directory_lvl1_file_path, dtype=str, na_values=\"\", keep_default_na=False)\n", "batch_columns = [col for col in df.columns if \"Batch\" in col]\n", "\n", "rename_batch_columns(df, batch_columns, df_batch_config, convergence_study, extension_percent)\n", "\n", "# Identify sample columns that need modification\n", "sample_columns = list(\n", "    df.loc[:, df.columns.str.startswith(\"Samples_as_Array\") & df.columns.str.contains(\"_training\")].columns\n", ")\n", "\n", "# Identify indexes to skip based on exclusion criteria\n", "list_index_to_skip = {\n", "    df.loc[\n", "        (df[\"Measure Name\"] == entry[\"measure_name\"]) & (df[\"Argument Name\"] == entry[\"argument_name\"])\n", "    ].index.values[0]: {\"measure_name\": entry[\"measure_name\"], \"argument_name\": entry[\"argument_name\"]}\n", "    for entry in map_parameter_to_skip_extension\n", "}\n", "\n", "# Apply modifications to sample columns\n", "for col in sample_columns:\n", "    print(\"### -----------------------------------------------------\")\n", "    print(\"### searching through column: {}\".format(col))\n", "    print(\"### -----------------------------------------------------\")\n", "    df[col] = pd.Series(enumerate(df[col].values)).map(\n", "        lambda x: modify_numbers(x[1], df, col, x[0], list_index_to_skip, extension_percent)\n", "    )\n", "\n", "# Create and validate new directory for the modified file\n", "new_dir = directory_lvl1.name.replace(\n", "    path_postfix_without_parameter_range_extension, path_postfix_with_parameter_range_extension\n", ")\n", "directory_lvl2 = directory_lvl1.with_name(new_dir)\n", "\n", "directory_lvl2.mkdir(parents=True, exist_ok=True)\n", "print(f\"Directory '{directory_lvl2}' ready.\")\n", "\n", "# Save the modified DataFrame as a new CSV file\n", "directory_lvl2_file_path = directory_lvl2 / directory_lvl1_file_path.name\n", "df.to_csv(directory_lvl2_file_path, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "179d", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}