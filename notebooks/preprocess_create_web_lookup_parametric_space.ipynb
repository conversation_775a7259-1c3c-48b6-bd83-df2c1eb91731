{"cells": [{"cell_type": "code", "execution_count": null, "id": "0", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Define input paths"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# # Need to add your specific 179D Teams path in here\n", "# list_of_paths = [\n", "#     Path(\"~/NREL/179d - Documents/General/\").expanduser(),  # for J<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "#     Path(\"~/OneDrive - NREL/General - 179d/\").expanduser(),  # for Jie\n", "#     Path(\"~/Library/CloudStorage/OneDrive-NREL/General\").expanduser(),  # for Alex\n", "#     Path(\"~/Library/CloudStorage/OneDrive-SharedLibraries-NREL/179d - General\").expanduser(),  # for Carlo\n", "# ]\n", "\n", "# # Initialize variables\n", "# found_dir = False\n", "# DIR_179 = \"\"\n", "\n", "# # Find relevant 179D Teams path\n", "# for teams_dir in list_of_paths:\n", "#     if teams_dir.is_dir():\n", "#         DIR_179 = teams_dir\n", "#         found_dir = True\n", "\n", "# # Raise error if nothing works\n", "# if found_dir == False:\n", "#     raise ValueError(\"Please configure the Path to the 179d folder on your machine\")\n", "# else:\n", "#     print(\"179D Teams folder connected to {}\".format(DIR_179))\n", "\n", "DIR_179 = Path(__file__).parent.parent"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# Path to web lookup file\n", "path_web_lookup = DIR_179 / \"files\" / \"web_lookups\" / \"179d_web_lookups.xlsx\"\n", "\n", "# Path to comstock csv\n", "path_comstock_csv = DIR_179 / \"files\" / \"comstock\" / \"baseline_metadata_and_annual_results.csv\"\n", "\n", "# Path to measure_spreadsheet\n", "spreadsheet_version = \"v16\"\n", "path_measure_spreadsheet = DIR_179 / \"files\" / \"measure_spreadsheets\" / \"measure_spreadsheet_\"+spreadsheet_version+\".xlsx\"\n", "\n", "assert path_web_lookup.is_file()\n", "assert path_comstock_csv.is_file()\n", "assert path_measure_spreadsheet.is_file()"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Define output path"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# output files for debugging\n", "path_weblookup_hvac_swh = (\n", "    DIR_179 / \"files\" / \"web_lookups\" / \"179d_web_lookups_hvac_swh.csv\"\n", ")\n", "path_weblookup_plus_others = (\n", "    DIR_179 / \"files\" / \"web_lookups\" / \"179d_web_lookups_plus_others.csv\"\n", ")\n", "path_weblookup_final = DIR_179 / \"files\" / \"web_lookups\" / \"179d_web_lookups_final.csv\""]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## Read comstock csv file"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(path_comstock_csv)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## Read weblookup parametric space spreadsheet"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["df_weblookup = pd.read_excel(path_web_lookup, sheet_name=\"parametric space\")\n", "df_weblookup"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Check if header information has been changed"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["hard_coded_header_definitions = [\n", "    {\"Name\": \"type 1 - name\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 1 - cooling dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - cooling dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - chiller cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - chiller cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - shw fuel\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 1 - heating gas coil efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - heating gas coil efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - boiler efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - boiler efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - heating dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 1 - heating dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - name\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 2 - cooling dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - cooling dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - chiller cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - chiller cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - shw fuel\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 2 - heating gas coil efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - heating gas coil efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - boiler efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - boiler efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - heating dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 2 - heating dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - name\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 3 - cooling dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - cooling dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - chiller cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - chiller cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - shw fuel\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 3 - heating gas coil efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - heating gas coil efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - boiler efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - boiler efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - heating dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 3 - heating dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - name\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 4 - cooling dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - cooling dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - chiller cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - chiller cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - shw fuel\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"type 4 - heating gas coil efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - heating gas coil efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - boiler efficiency min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - boiler efficiency max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - heating dx coil cop min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"type 4 - heating dx coil cop max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"wwr min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"wwr max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"insulation wall min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"insulation wall max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"insulation roof min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"insulation roof max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"window u value min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"window u value max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"window shgc min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"window shgc max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"lpd min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"lpd max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"number of floors\", \"Data Type\": \"int\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"floor area min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"floor area max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"aspect ratio min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"aspect ratio max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"weather\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"water heater fuel type\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "    {\"Name\": \"water heater performance category ratio min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"water heater performance category ratio max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"water heater heater capacity ratio min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"water heater heater capacity ratio max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"water heater first hour rating ratio min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"water heater first hour rating ratio max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"building rotation min\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"building rotation max\", \"Data Type\": \"float\", \"Continuous-Discrete\": \"continuous\"},\n", "    {\"Name\": \"skip for heat or energy recovery ventilator\", \"Data Type\": \"string\", \"Continuous-Discrete\": \"discrete\"},\n", "]"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["revise accordingly if you see any errors raised from the code below"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["for header_info in hard_coded_header_definitions:\n", "\n", "    # check data format\n", "    if header_info[\"Name\"] in df_weblookup[\"Name\"].values:\n", "        data_type_original = header_info[\"Data Type\"]\n", "        data_type_new = df_weblookup.loc[df_weblookup[\"Name\"] == header_info[\"Name\"], \"Data Type\"]\n", "        if not data_type_new.empty:\n", "            data_type_new = data_type_new.iloc[0]\n", "            if not data_type_original == data_type_new:\n", "                raise ValueError(\n", "                    f\"header information regarding data type changed from previous description: in spreadsheet = {data_type_new} | in previous description = {data_type_original}\"\n", "                )\n", "\n", "        # check data type\n", "        data_type_original = header_info[\"Continuous-Discrete\"]\n", "        data_type_new = df_weblookup.loc[df_weblookup[\"Name\"] == header_info[\"Name\"], \"Continuous-Discrete\"]\n", "        if not data_type_new.empty:\n", "            data_type_new = data_type_new.iloc[0]\n", "            if not data_type_original == data_type_new:\n", "                raise ValueError(\n", "                    f\"header information regarding continuous/discrete changed from previous description: in spreadsheet = {data_type_new} | in previous description = {data_type_original}\"\n", "                )\n", "    else:\n", "        raise ValueError(f\"header information name {header_info['Name']} not found in the spreadsheet\")\n", "\n", "print(\"All header information checks passed.\")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## Define hard coded parameters"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# this is the list we are trying to model\n", "# the name should exactly match with input args for create_bar and create_typical measures\n", "building_types = [\n", "    \"SmallOffice\",\n", "    \"Warehouse\",\n", "    \"MediumOffice\",\n", "    \"PrimarySchool\",\n", "    \"RetailStripmall\",\n", "    \"SecondarySchool\",\n", "    \"RetailStandalone\",\n", "    \"SmallHotel\",\n", "]\n", "\n", "# hvac types list is based on post-processing of comstock csv files\n", "# it is selecting the top 3 (or 4 if floor area coverage is not enough) common hvac types for each building type\n", "# currently, it is based on manual data processing\n", "hvac_types_for_bldg_type = {\n", "    \"SmallOffice\": [\"PSZ-HP\", \"PSZ-AC with electric coil\", \"PSZ-AC with gas coil\"],\n", "    \"RetailStripmall\": [\"PSZ-HP\", \"PSZ-AC with electric coil\", \"PSZ-AC with gas coil\"],\n", "    \"Warehouse\": [\"PSZ-AC with gas coil\", \"PSZ-AC with electric coil\", \"PVAV with gas boiler reheat\"],\n", "    \"MediumOffice\": [\n", "        \"VAV air-cooled chiller with gas boiler reheat\",\n", "        \"PVAV with gas heat with electric reheat\",\n", "        \"PVAV with gas boiler reheat\",\n", "        \"PSZ-AC with gas coil\",\n", "    ],\n", "    \"PrimarySchool\": [\n", "        \"PVAV with gas boiler reheat\",\n", "        \"PSZ-AC with gas coil\",\n", "        \"VAV air-cooled chiller with gas boiler reheat\",\n", "        \"PSZ-HP\",\n", "    ],\n", "    \"SecondarySchool\": [\n", "        \"VAV air-cooled chiller with gas boiler reheat\",\n", "        \"PSZ-AC with gas coil\",\n", "        \"PSZ-HP\",\n", "        \"PVAV with gas boiler reheat\",\n", "    ],\n", "    \"RetailStandalone\": [\n", "        \"PSZ-AC with gas coil\",\n", "        \"PSZ-AC with electric coil\",\n", "        \"PVAV with gas boiler reheat\",\n", "    ],\n", "    \"SmallHotel\": [\n", "        \"PTAC with electric coil\",\n", "        \"PTHP\",\n", "        \"PTAC with gas boiler\",\n", "    ],\n", "}"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## sub-methods for data processing"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["def get_min_max(df, col_name):\n", "\n", "    df[col_name] = df[col_name].replace(0, np.nan)  # removing zeros\n", "\n", "    value_min = round(df[col_name].min(), 3)\n", "    value_max = round(df[col_name].max(), 3)\n", "\n", "    return value_min, value_max\n", "\n", "\n", "def fill_in_values(df_weblookup, df_comstock, parameters, system_type, building_type, entry, add_type_num, idx):\n", "\n", "    # set prefix and label for string search\n", "    if add_type_num:\n", "        parameters_at_entry = parameters[system_type]\n", "        label_lookup = parameters_at_entry[entry][\"label_in_lookup\"]\n", "        prefix = f\"type {idx+1} - \" + label_lookup\n", "    else:\n", "        parameters_at_entry = parameters\n", "        label_lookup = parameters_at_entry[entry][\"label_in_lookup\"]\n", "        prefix = label_lookup\n", "\n", "    # fill in values\n", "    if \"label_value\" not in parameters_at_entry[entry].keys():\n", "\n", "        # get label\n", "        label_comstock = parameters_at_entry[entry][\"label_in_comstock\"]\n", "        print(f\"--- label_comstock = {label_comstock}\")\n", "\n", "        # exceptions which needs more tweak from raw values\n", "        if label_comstock == \"out.params.average_wall_u_value..btu_per_ft2_f_hr\":\n", "            df_comstock[\"out.params.average_wall_u_value..btu_per_ft2_f_hr\"] = (\n", "                1 / df_comstock[\"out.params.average_wall_u_value..btu_per_ft2_f_hr\"]\n", "            )  # u-value to r-value\n", "        if label_comstock == \"out.params.average_roof_u_value..btu_per_ft2_f_hr\":\n", "            df_comstock[\"out.params.average_roof_u_value..btu_per_ft2_f_hr\"] = (\n", "                1 / df_comstock[\"out.params.average_roof_u_value..btu_per_ft2_f_hr\"]\n", "            )  # u-value to r-value\n", "\n", "        # get min/max values\n", "        value_min, value_max = get_min_max(df_comstock, label_comstock)\n", "        print(f\"--- value_min = {value_min}\")\n", "        print(f\"--- value_max = {value_max}\")\n", "\n", "        # check values\n", "        if label_lookup == \"cooling dx coil cop\" and value_min <= 0:\n", "            raise ValueError(f\"*** value for [{label_comstock}] is zero or below so stopping the process: {value_min}\")\n", "\n", "        # check filtered datapoints\n", "        if df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" min\"), :].shape[0] == 0:\n", "            raise ValueError(f\"*** cannot find a row for [{label_lookup}] minimum from web lookup\")\n", "        if df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" max\"), :].shape[0] == 0:\n", "            raise ValueError(f\"*** cannot find a row for [{label_lookup}] maximum from web lookup\")\n", "\n", "        # assign to cell\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" min\"), building_type] = value_min\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" max\"), building_type] = value_max\n", "\n", "    else:\n", "\n", "        # get label\n", "        label_value = parameters_at_entry[entry][\"label_value\"]\n", "        print(f\"--- label_value = {label_value}\")\n", "\n", "        # exception\n", "        if entry == \"number of stories\":\n", "\n", "            # get min/max values\n", "            value_min, value_max = get_min_max(df_comstock, label_value)\n", "            print(f\"--- value_min for {entry} = {value_min}\")\n", "            print(f\"--- value_max for {entry} = {value_max}\")\n", "\n", "            label_value = list(range(value_min, value_max + 1))\n", "            label_value = \", \".join(map(str, label_value))\n", "            print(f\"--- final value for {entry} = {label_value}\")\n", "\n", "        # check filtered datapoints\n", "        if df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix), :].shape[0] == 0:\n", "            raise ValueError(f\"*** cannot find a row for [{label_lookup}] from web lookup\")\n", "\n", "        # assign to cell\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix), building_type] = label_value\n", "\n", "    return df_weblookup\n", "\n", "\n", "def fill_in_with_loc(df_weblookup, hard_coded_parameters, entry, bldg_type, label_lookup, prefix):\n", "\n", "    if hard_coded_parameters[entry][\"min\"] is not None:\n", "        print(f\"--- applying {label_lookup} with min value {hard_coded_parameters[entry]['min']}\")\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" min\"), bldg_type] = hard_coded_parameters[entry][\n", "            \"min\"\n", "        ]\n", "\n", "    if hard_coded_parameters[entry][\"max\"] is not None:\n", "        print(f\"--- applying {label_lookup} with max value {hard_coded_parameters[entry]['max']}\")\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix + \" max\"), bldg_type] = hard_coded_parameters[entry][\n", "            \"max\"\n", "        ]\n", "\n", "    if hard_coded_parameters[entry][\"value\"] is not None:\n", "        print(f\"--- applying {label_lookup} with fixed value {hard_coded_parameters[entry]['value']}\")\n", "        df_weblookup.loc[df_weblookup[\"Name\"].str.contains(prefix), bldg_type] = hard_coded_parameters[entry][\"value\"]\n", "\n", "    return df_weblookup\n", "\n", "\n", "def fill_in_overrides(df_weblookup, hard_coded_parameters, entry, hvac_types_for_bldg_type, bldg_type, label_lookup):\n", "\n", "    if \"hvac_types\" in hard_coded_parameters[entry].keys():\n", "        print(f\"--- considering applicable hvac types for {entry}\")\n", "        hvac_types_to_apply = hard_coded_parameters[entry][\"hvac_types\"]\n", "\n", "        for hvac_type_to_apply in hvac_types_to_apply:\n", "\n", "            # check if applicable hvac type is available for building type\n", "            if hvac_type_to_apply not in hvac_types_for_bldg_type[bldg_type]:\n", "                print(f\"--- hvac type {hvac_type_to_apply} not found in a list for {bldg_type}\")\n", "                continue\n", "\n", "            print(f\"--- hvac type {hvac_type_to_apply} applicable for {bldg_type}\")\n", "            print(f\"--- hvac_type_to_apply = {hvac_type_to_apply}\")\n", "            print(f\"--- bldg_type = {bldg_type}\")\n", "\n", "            # get hvac type number\n", "            hvac_type_num = (\n", "                df_weblookup.loc[df_weblookup[bldg_type] == hvac_type_to_apply, \"Name\"].iloc[0].split(\" - \")[0]\n", "            )\n", "            print(f\"--- hvac_type_num = {hvac_type_num}\")\n", "\n", "            # set prefix for string search\n", "            prefix = hvac_type_num + \" - \" + label_lookup\n", "            print(f\"--- prefix = {prefix}\")\n", "\n", "            # fill in data\n", "            df_weblookup = fill_in_with_loc(df_weblookup, hard_coded_parameters, entry, bldg_type, label_lookup, prefix)\n", "\n", "    else:\n", "\n", "        # set prefix for string search\n", "        prefix = label_lookup\n", "        print(f\"--- prefix = {prefix}\")\n", "\n", "        # fill in data\n", "        df_weblookup = fill_in_with_loc(df_weblookup, hard_coded_parameters, entry, bldg_type, label_lookup, prefix)\n", "\n", "    return df_weblookup"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["## update web lookup: hvac"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# this mapping connects row name in web lookup to column name in comstock csv\n", "# if label_in_comstock is used, then it extracts min/max values\n", "# if label_value is used, then it just uses a specific value (and not extracting min/max)\n", "hvac_parameters = {\n", "    \"PSZ-AC with gas coil\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"heating gas coil efficiency\",\n", "            \"label_in_comstock\": \"out.params.gas_coil_average_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"PSZ-AC with electric coil\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": None,\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"Electricity\",\n", "        },\n", "    },\n", "    \"PSZ-HP\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"heating dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_heating_design_cop..cop\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"Electricity\",\n", "        },\n", "    },\n", "    \"PVAV with gas boiler reheat\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"boiler efficiency\",\n", "            \"label_in_comstock\": \"out.params.boiler_cap_weight_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"VAV air-cooled chiller with gas boiler reheat\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"chiller cop\",\n", "            \"label_in_comstock\": \"out.params.design_chiller_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"boiler efficiency\",\n", "            \"label_in_comstock\": \"out.params.boiler_cap_weight_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"PVAV with gas heat with electric reheat\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"heating gas coil efficiency\",\n", "            \"label_in_comstock\": \"out.params.gas_coil_average_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"PTAC with electric coil\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": None,\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"PTAC with gas boiler\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"boiler efficiency\",\n", "            \"label_in_comstock\": \"out.params.boiler_cap_weight_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"NaturalGas\",\n", "        },\n", "    },\n", "    \"PTHP\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"heating dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_heating_design_cop..cop\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"Electricity\",\n", "        },\n", "    },\n", "    \"Residential AC with residential forced air furnace\": {\n", "        \"eff_cooling\": {\n", "            \"label_in_lookup\": \"cooling dx coil cop\",\n", "            \"label_in_comstock\": \"out.params.dx_cooling_design_cop..cop\",\n", "        },\n", "        \"eff_heating\": {\n", "            \"label_in_lookup\": \"heating gas coil efficiency\",\n", "            \"label_in_comstock\": \"out.params.gas_coil_average_efficiency\",\n", "        },\n", "        \"swh_fuel\": {\n", "            \"label_in_lookup\": \"shw fuel\",\n", "            \"label_value\": \"Electricity\",\n", "        },\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["for building_type in building_types:\n", "\n", "    # filter for specific building type\n", "    df_bt = df[df[\"in.comstock_building_type\"] == building_type].copy()\n", "    system_types = hvac_types_for_bldg_type[building_type]\n", "\n", "    print(\"### ============================================================\")\n", "    print(f\"### building_type = {building_type}\")\n", "\n", "    # add building type if dataframe does not have the building type\n", "    if building_type not in df_weblookup.columns:\n", "        df_weblookup[building_type] = \"\"\n", "        print(f\"### adding {building_type} to the web lookup since it does not exists.\")\n", "\n", "    for idx, system_type in enumerate(system_types):\n", "\n", "        # filter for specific hvac type\n", "        df_bt_system = df_bt[df_bt[\"in.hvac_system_type\"] == system_type].copy()\n", "        print(\"### ------------------------------------------------------------\")\n", "        print(f\"### system_type {idx+1} = {system_type}\")\n", "        print(f\"### filtered datapoint size = {df_bt_system.shape}\")\n", "\n", "        # check data\n", "        if df_bt_system.shape[0] == 0:\n", "            raise ValueError(f\"*** no datapoints for {building_type} and for {system_type}\")\n", "\n", "        # fill in information in web lookup spreadsheet based on\n", "        for entry in hvac_parameters[system_type].keys():\n", "            print(f\"### entry = {entry}\")\n", "\n", "            # skip if there is no information for the entry\n", "            if hvac_parameters[system_type][entry] is None:\n", "                print(f\"### skipping {entry} since no information is provided\")\n", "                continue\n", "\n", "            # get label\n", "            label_lookup = hvac_parameters[system_type][entry][\"label_in_lookup\"]\n", "            print(f\"--- label_lookup = {label_lookup}\")\n", "\n", "            # update hvac type name to web lookup\n", "            df_weblookup.loc[df_weblookup[\"Name\"].str.contains(f\"type {idx+1} - name\"), building_type] = system_type\n", "\n", "            # fill in values to web lookup\n", "            df_weblookup = fill_in_values(\n", "                df_weblookup, df_bt_system, hvac_parameters, system_type, building_type, entry, True, idx\n", "            )\n", "\n", "# save file\n", "df_weblookup.to_csv(path_weblookup_hvac_swh)"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["## update web lookup: others"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["# this mapping connects row name in web lookup to column name in comstock csv\n", "# if label_in_comstock is used, then it extracts min/max values\n", "# if label_value is used, then it just uses a specific value (and not extracting min/max)\n", "other_parameters = {\n", "    \"wwr\": {\n", "        \"label_in_lookup\": \"wwr\",\n", "        \"label_in_comstock\": \"out.params.window_to_wall_ratio\",\n", "    },\n", "    \"wall u value\": {\n", "        \"label_in_lookup\": \"insulation wall\",\n", "        \"label_in_comstock\": \"out.params.average_wall_u_value..btu_per_ft2_f_hr\",\n", "    },\n", "    \"roof u value\": {\n", "        \"label_in_lookup\": \"insulation roof\",\n", "        \"label_in_comstock\": \"out.params.average_roof_u_value..btu_per_ft2_f_hr\",\n", "    },\n", "    \"window u value\": {\n", "        \"label_in_lookup\": \"window u value\",\n", "        \"label_in_comstock\": \"out.params.average_window_u_value..btu_per_ft2_f_hr\",\n", "    },\n", "    \"window shgc\": {\n", "        \"label_in_lookup\": \"window shgc\",\n", "        \"label_in_comstock\": \"out.params.average_window_shgc\",\n", "    },\n", "    \"lpd\": {\n", "        \"label_in_lookup\": \"lpd\",\n", "        \"label_in_comstock\": \"out.params.interior_lighting_power_density..w_per_ft2\",\n", "    },\n", "    \"floor area\": {\n", "        \"label_in_lookup\": \"floor area\",\n", "        \"label_in_comstock\": \"in.sqft\",\n", "    },\n", "    \"aspect ratio\": {\n", "        \"label_in_lookup\": \"aspect ratio\",\n", "        \"label_in_comstock\": \"in.aspect_ratio\",\n", "    },\n", "    \"climate zone\": {\n", "        \"label_in_lookup\": \"weather\",\n", "        \"label_value\": \"1A, 2A, 2B, 3A, 3B, 3C, 4A, 4B, 4C, 5A, 5B, 5C, 6A, 6B, 7, 8\",\n", "    },\n", "    \"erv skip\": {\n", "        \"label_in_lookup\": \"skip for heat or energy recovery ventilator\",\n", "        \"label_value\": \"TRUE,FALSE\",\n", "    },\n", "    \"number of stories\": {\n", "        \"label_in_lookup\": \"number of floors\",\n", "        \"label_value\": \"in.number_of_stories\",\n", "    },\n", "}\n", "\n", "# Extract all \"label_in_comstock\" values from the other_parameters dictionary\n", "label_in_comstock_values = [\n", "    param[\"label_in_comstock\"] for param in other_parameters.values() if \"label_in_comstock\" in param\n", "]\n", "\n", "# Check for missing columns\n", "missing_columns = [col for col in label_in_comstock_values if col not in df.columns]\n", "if missing_columns:\n", "    raise ValueError(f\"The following columns are missing in the comstock data: {missing_columns}\")\n", "else:\n", "    print(\"All columns are available in the comstock data\")"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["for building_type in building_types:\n", "\n", "    # filter for specific building type\n", "    df_bt = df[df[\"in.comstock_building_type\"] == building_type].copy()\n", "\n", "    print(\"### ============================================================\")\n", "    print(f\"### building_type = {building_type}\")\n", "\n", "    # fill in information in web lookup spreadsheet based on\n", "    for entry in other_parameters.keys():\n", "        print(\"### ------------------------------------------------------------\")\n", "        print(f\"### entry = {entry}\")\n", "\n", "        # skip if there is no information for the entry\n", "        if other_parameters[entry] is None:\n", "            print(f\"### skipping {entry} since no information is provided\")\n", "            continue\n", "\n", "        # get label\n", "        label_lookup = other_parameters[entry][\"label_in_lookup\"]\n", "        print(f\"--- label_lookup = {label_lookup}\")\n", "\n", "        # fill in values to web lookup\n", "        df_weblookup = fill_in_values(df_weblookup, df_bt, other_parameters, None, building_type, entry, False, None)\n", "\n", "# save file\n", "df_weblookup.to_csv(path_weblookup_plus_others)"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["## update web lookup: manual overrides"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["# hard-coded values\n", "# there are reasons for this override\n", "# either min/max or value should be used\n", "hard_coded_parameters = {\n", "    \"furnace efficiency\": {\n", "        \"label_lookup\": \"heating gas coil efficiency\",\n", "        \"building_types\": \"all\",\n", "        \"hvac_types\": [\n", "            \"PSZ-AC with gas coil\",\n", "            \"PVAV with gas heat with electric reheat\",\n", "            \"Residential AC with residential forced air furnace\",\n", "        ],\n", "        \"min\": 0.8,\n", "        \"max\": 0.98,\n", "        \"value\": None,\n", "    },\n", "    \"dx heating coil efficiency\": {\n", "        \"label_lookup\": \"heating dx coil cop\",\n", "        \"building_types\": \"all\",\n", "        \"hvac_types\": [\n", "            \"PSZ-HP\",\n", "            \"PTHP\",\n", "        ],\n", "        \"min\": None,\n", "        \"max\": 6.0,\n", "        \"value\": None,\n", "    },\n", "    \"dx cooling coil efficiency\": {\n", "        \"label_lookup\": \"cooling dx coil cop\",\n", "        \"building_types\": \"all\",\n", "        \"hvac_types\": [\n", "            \"PSZ-HP\",\n", "            \"PSZ-AC with electric coil\",\n", "            \"PSZ-AC with gas coil\",\n", "            \"PVAV with gas boiler reheat\",\n", "            \"PVAV with gas heat with electric reheat\",\n", "            \"PTHP\",\n", "            \"PTAC with electric coil\",\n", "            \"PTAC with gas boiler\",\n", "        ],\n", "        \"min\": None,\n", "        \"max\": 6.0,\n", "        \"value\": None,\n", "    },\n", "    \"chiller efficiency\": {\n", "        \"label_lookup\": \"chiller cop\",\n", "        \"building_types\": \"all\",\n", "        \"hvac_types\": [\n", "            \"VAV air-cooled chiller with gas boiler reheat\",\n", "        ],\n", "        \"min\": None,\n", "        \"max\": 6.0,\n", "        \"value\": None,\n", "    },\n", "    \"boiler efficiency\": {\n", "        \"label_lookup\": \"boiler efficiency\",\n", "        \"building_types\": \"all\",\n", "        \"hvac_types\": [\n", "            \"PVAV with gas boiler reheat\",\n", "            \"VAV air-cooled chiller with gas boiler reheat\",\n", "            \"PTAC with gas boiler\",\n", "        ],\n", "        \"min\": None,\n", "        \"max\": 0.95,\n", "        \"value\": None,\n", "    },\n", "    \"wwr\": {\n", "        \"label_lookup\": \"wwr\",\n", "        \"building_types\": \"all\",\n", "        \"min\": None,\n", "        \"max\": 0.4,\n", "        \"value\": None,\n", "    },\n", "    \"wall r value 1\": {\n", "        \"label_lookup\": \"insulation wall\",\n", "        \"building_types\": [\n", "            \"SmallOffice\",\n", "            \"MediumOffice\",\n", "            \"PrimarySchool\",\n", "            \"RetailStripmall\",\n", "            \"SecondarySchool\",\n", "            \"RetailStandalone\",\n", "            \"SmallHotel\",\n", "        ],\n", "        \"min\": 13.4,\n", "        \"max\": 30.0,\n", "        \"value\": None,\n", "    },\n", "    \"wall r value 2\": {\n", "        \"label_lookup\": \"insulation wall\",\n", "        \"building_types\": [\"Warehouse\"],\n", "        \"min\": 5.9,\n", "        \"max\": 30.0,\n", "        \"value\": None,\n", "    },\n", "    \"roof r value 1\": {\n", "        \"label_lookup\": \"insulation roof\",\n", "        \"building_types\": [\n", "            \"SmallOffice\",\n", "            \"MediumOffice\",\n", "            \"PrimarySchool\",\n", "            \"RetailStripmall\",\n", "            \"SecondarySchool\",\n", "            \"RetailStandalone\",\n", "            \"SmallHotel\",\n", "        ],\n", "        \"min\": 19.7,\n", "        \"max\": 50.0,\n", "        \"value\": None,\n", "    },\n", "    \"roof r value 2\": {\n", "        \"label_lookup\": \"insulation roof\",\n", "        \"building_types\": [\"Warehouse\"],\n", "        \"min\": 7.3,\n", "        \"max\": 50.0,\n", "        \"value\": None,\n", "    },\n", "    \"window u value\": {\n", "        \"label_lookup\": \"window u value\",\n", "        \"building_types\": \"all\",\n", "        \"min\": 0.2,\n", "        \"max\": 0.45,\n", "        \"value\": None,\n", "    },\n", "    \"window shgc\": {\n", "        \"label_lookup\": \"window shgc\",\n", "        \"building_types\": \"all\",\n", "        \"min\": None,\n", "        \"max\": 0.45,\n", "        \"value\": None,\n", "    },\n", "    \"number of stories 1\": {\n", "        \"label_lookup\": \"number of floors\",\n", "        \"building_types\": [\"SmallOffice\", \"RetailStripmall\"],\n", "        \"min\": None,\n", "        \"max\": None,\n", "        \"value\": \"1,2,3\",\n", "    },\n", "    \"number of stories 2\": {\n", "        \"label_lookup\": \"number of floors\",\n", "        \"building_types\": [\"MediumOffice\"],\n", "        \"min\": None,\n", "        \"max\": None,\n", "        \"value\": \"1,2,3,4,5\",\n", "    },\n", "    \"floor area 1\": {\n", "        \"label_lookup\": \"floor area\",\n", "        \"building_types\": [\"SmallOffice\", \"RetailStripmall\"],\n", "        \"min\": None,\n", "        \"max\": 24999,\n", "        \"value\": None,\n", "    },\n", "    \"floor area 2\": {\n", "        \"label_lookup\": \"floor area\",\n", "        \"building_types\": [\"MediumOffice\"],\n", "        \"min\": 25000,\n", "        \"max\": 150000,\n", "        \"value\": None,\n", "    },\n", "    \"floor area 3\": {\n", "        \"label_lookup\": \"floor area\",\n", "        \"building_types\": [\"PrimarySchool\"],\n", "        \"min\": 40000,\n", "        \"max\": None,\n", "        \"value\": None,\n", "    },\n", "    \"floor area 4\": {\n", "        \"label_lookup\": \"floor area\",\n", "        \"building_types\": [\"SecondarySchool\"],\n", "        \"min\": 100000,\n", "        \"max\": None,\n", "        \"value\": None,\n", "    },\n", "    \"building rotation\": {\n", "        \"label_lookup\": \"building rotation\",\n", "        \"building_types\": \"all\",\n", "        \"min\": 0,\n", "        \"max\": 180,\n", "        \"value\": None,\n", "    },\n", "    \"climate zone\": {\n", "        \"label_lookup\": \"weather\",\n", "        \"building_types\": \"all\",\n", "        \"min\": None,\n", "        \"max\": None,\n", "        \"value\": \"1A, 2A, 2B, 3A, 3B, 3C, 4A, 4B, 4C, 5A, 5B, 5C, 6A, 6B, 7, 8\",\n", "    },\n", "    \"swh performance category\": {\n", "        \"label_lookup\": \"water heater performance category ratio\",\n", "        \"building_types\": \"all\",\n", "        \"min\": 0.0,\n", "        \"max\": 1.0,\n", "        \"value\": None,\n", "    },\n", "    \"swh heater capacity\": {\n", "        \"label_lookup\": \"water heater heater capacity ratio\",\n", "        \"building_types\": \"all\",\n", "        \"min\": 0.0,\n", "        \"max\": 1.0,\n", "        \"value\": None,\n", "    },\n", "    \"swh first hour rating\": {\n", "        \"label_lookup\": \"water heater first hour rating ratio\",\n", "        \"building_types\": \"all\",\n", "        \"min\": 0.0,\n", "        \"max\": 1.0,\n", "        \"value\": None,\n", "    },\n", "    \"lighting power density 1\": {\n", "        \"label_lookup\": \"lpd\",\n", "        \"building_types\": [\"RetailStripmall\", \"RetailStandalone\"],\n", "        \"min\": None,\n", "        \"max\": 1.5,\n", "        \"value\": None,\n", "    },\n", "    \"lighting power density 2\": {\n", "        \"label_lookup\": \"lpd\",\n", "        \"building_types\": [\"PrimarySchool\", \"SecondarySchool\"],\n", "        \"min\": None,\n", "        \"max\": 1.2,\n", "        \"value\": None,\n", "    },\n", "    \"lighting power density 3\": {\n", "        \"label_lookup\": \"lpd\",\n", "        \"building_types\": [\"SmallOffice\", \"MediumOffice\"],\n", "        \"min\": None,\n", "        \"max\": 1.0,\n", "        \"value\": None,\n", "    },\n", "    \"lighting power density 4\": {\n", "        \"label_lookup\": \"lpd\",\n", "        \"building_types\": [\"Warehouse\"],\n", "        \"min\": None,\n", "        \"max\": 0.8,\n", "        \"value\": None,\n", "    },\n", "    \"lighting power density 5\": {\n", "        \"label_lookup\": \"lpd\",\n", "        \"building_types\": [\"SmallHotel\"],\n", "        \"min\": None,\n", "        \"max\": 1.0,\n", "        \"value\": None,\n", "    },\n", "    \"water heater fuel type\": {\n", "        \"label_lookup\": \"water heater fuel type\",\n", "        \"building_types\": \"all\",\n", "        \"min\": None,\n", "        \"max\": None,\n", "        \"value\": \"Electricity, NaturalGas\",\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["# get all building types\n", "bldg_types_all = list(\n", "    df_weblookup.drop(columns=[\"Name\", \"Description\", \"Unit\", \"Data Type\", \"Continuous-Discrete\"]).columns\n", ")\n", "\n", "for entry in hard_coded_parameters.keys():\n", "    print(\"### ============================================================\")\n", "    print(f\"### entry = {entry}\")\n", "\n", "    # get label\n", "    label_lookup = hard_coded_parameters[entry][\"label_lookup\"]\n", "    print(f\"--- label_lookup = {label_lookup}\")\n", "\n", "    # check applicable building types\n", "    if hard_coded_parameters[entry][\"building_types\"] == \"all\":\n", "        bldg_types = bldg_types_all\n", "    else:\n", "        bldg_types = hard_coded_parameters[entry][\"building_types\"]\n", "\n", "    # fill in data\n", "    for bldg_type in bldg_types:\n", "        print(\"### ------------------------------------------------------------\")\n", "        print(f\"--- applying to all building types: {bldg_type}\")\n", "\n", "        # check data\n", "        if df_weblookup.loc[df_weblookup[\"Name\"].str.contains(label_lookup), :].shape[0] == 0:\n", "            raise ValueError(f\"*** not data available for {label_lookup}\")\n", "\n", "        # fill in data\n", "        df_weblookup = fill_in_overrides(\n", "            df_weblookup, hard_coded_parameters, entry, hvac_types_for_bldg_type, bldg_type, label_lookup\n", "        )\n", "\n", "# save file\n", "df_weblookup.to_csv(path_weblookup_final)"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["## Save web lookup parametric space to original excel file"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["from openpyxl import load_workbook\n", "from openpyxl.styles import PatternFill\n", "from openpyxl.utils import get_column_letter\n", "\n", "sheet_name = \"parametric space\"\n", "\n", "# Save the DataFrame to an Excel file\n", "with pd.ExcelWriter(path_web_lookup, engine=\"openpyxl\", mode=\"a\", if_sheet_exists=\"replace\") as writer:\n", "    df_weblookup.to_excel(writer, sheet_name=sheet_name, index=False)\n", "\n", "# Load the workbook and the specific sheet\n", "workbook = load_workbook(path_web_lookup)\n", "worksheet = workbook[sheet_name]\n", "\n", "# Define fill styles for each type\n", "fills = {\n", "    \"type 1\": <PERSON><PERSON><PERSON><PERSON>(start_color=\"dfc27d\", end_color=\"dfc27d\", fill_type=\"solid\"),  # Lighter Yellow\n", "    \"type 2\": <PERSON><PERSON><PERSON><PERSON>(start_color=\"c7eae5\", end_color=\"c7eae5\", fill_type=\"solid\"),  # Lighter Blue\n", "    \"type 3\": <PERSON><PERSON><PERSON><PERSON>(start_color=\"f6e8c3\", end_color=\"f6e8c3\", fill_type=\"solid\"),  # Lighter Red\n", "    \"type 4\": <PERSON><PERSON><PERSON><PERSON>(start_color=\"80cdc1\", end_color=\"80cdc1\", fill_type=\"solid\"),  # Lighter Green\n", "}\n", "\n", "# Iterate over the rows starting from row 2 to skip the header\n", "for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):\n", "    cell_value = str(row[0].value).lower()\n", "    for key, fill in fills.items():\n", "        if key in cell_value:  # Check the value in the first column (A column)\n", "            for cell in row:  # Apply the corresponding fill to every cell in the row\n", "                cell.fill = fill\n", "            break  # Stop checking other types once a match is found to prevent overwriting colors\n", "\n", "# Adjusting column widths\n", "limit_length = 15\n", "for col in worksheet.columns:\n", "    max_length = 0\n", "    column = col[0].column  # Get the column name\n", "    for cell in col:\n", "        try:\n", "            if len(str(cell.value)) > max_length:\n", "                max_length = len(str(cell.value))\n", "        except (<PERSON><PERSON><PERSON><PERSON>, ValueError):\n", "            pass\n", "    if max_length > limit_length:\n", "        max_length = limit_length\n", "    adjusted_width = max_length * 1.2\n", "    worksheet.column_dimensions[get_column_letter(column)].width = adjusted_width\n", "\n", "# Save the changes to the workbook\n", "workbook.save(path_web_lookup)"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 5}