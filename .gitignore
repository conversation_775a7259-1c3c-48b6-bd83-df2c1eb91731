/.bundle/
/vendor
/vendor/bundle
/.yardoc
/.ruby-version
/.python-version
/.venv
/Gemfile.lock
/gems
/_yardoc/
/coverage/
/doc/
/myenv/
/pkg/
/spec/reports/
/spec/test/
/spec/openstudio/core/output/*
/tmp/
/test/
/lib/measures/test_results
/lib/measures/.rubocop*
/lib/measures/staged
/lib/measures/staged/*
/spec/files/models/
/spec/files/weather/
/spec/tests/test_results/
spec_test_results/
/test_results/
/vendor/
/edge_samples_configs.json
files/edge_samples_configs.json

.DS_Store

# rspec failure tracking
.rspec_status

# Ignore IDE files
/.idea
*.rubocop-http*
.vscode

# measures tests
lib/measures/.rubocop.yml
lib/measures/test_results/*

# runner conf
runner.conf

# Sizing run made as part of spec that tests typical_building_from_model method
SR1

/outputs
**/measures/*/tests/output
unzipped_analysis
notebook/.ipynb_checkpoints
notebook/*.html
notebook/*.csv
notebook/converted*
notebook/tests/models/*/
.ruby-version
.tool-versions
.env
configs.yml
measure_space.json
parametric_space.json
**/run/
rubocop.json
