# 179D

## 

### Edge Case Files

#### Edge Cases Test Data (`lib/tests/edge_cases/edge_cases.xlsx`)
Excel spreadsheet containing test data across multiple sheets:
- **envelope**: Roof/wall R-values, window properties, SHGC values
- **lpd**: Lighting power density values
- **shw**: Service hot water efficiency parameters
- **hvac**: Heating/cooling efficiency values

Each sheet defines minimum and maximum values for different building types, HVAC systems, and climate zones.

#### Edge Samples Configuration (`edge_samples_configs.json.template`)
Template file that defines how parameters should be handled in edge case testing. **This file can be customized** to suit your specific project needs:

```json
{
  "building_types": {
    "SmallOffice": {
      "hvac_system_types": {
        "type1": {
          "climate_zones": {
            "1A": {
              "parameters_on_edges": {
                "Aspect Ratio": {
                  "measure_name": "create_bar_from_building_type_ratios_comstock",
                  "argument_name": "ns_to_ew_ratio"
                }
              },
              "parameters_fixed": {
                "Building Rotation": {
                  "measure_name": "create_bar_from_building_type_ratios_comstock",
                  "argument_name": "building_rotation",
                  "values": [0]
                }
              },
              "parameters_range": {
                "Roof R-value": {
                  "measure_name": "IncreaseInsulationRValueForRoofs",
                  "argument_name": "r_value",
                  "values": [14.408, 51.9]
                }
              }
            }
          }
        }
      }
    }
  }
}
```

**Parameter Categories:**
- `parameters_on_edges`: Parameters tested at min/max extremes only
- `parameters_fixed`: Parameters with constant values across all scenarios
- `parameters_range`: Parameters that vary within specified min/max ranges

#### Python Test Calculator (`lib/tests/edge_cases/test_calculator179.py`)
Comprehensive test suite that:
- Validates 179D tax deduction calculations using edge case scenarios
- Reads test data from the Excel spreadsheet
- Exports configuration data for the Ruby workflow
- Ensures calculation accuracy across extreme parameter combinations
