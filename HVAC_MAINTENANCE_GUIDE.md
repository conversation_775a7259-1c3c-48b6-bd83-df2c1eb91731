# HVAC System Type Maintenance Guide

## Overview
The `test_calculator179.py` script now uses a **dynamic approach** to handle HVAC system types. This means the script automatically adapts to changes in the input data without requiring manual updates to hardcoded lists.

## How It Works

### 1. Dynamic Detection
- The script reads the Excel file to detect which HVAC types are actually present in the data
- Only HVAC types that exist in both the `HVACSystemType` enum and the input spreadsheet are processed
- This prevents errors when the spreadsheet contains HVAC types not yet defined in the code

### 2. Complete Mappings with Dynamic Filtering
Three key data structures are managed this way:

- **`HVAC_ALIASES`**: Maps HVAC enum types to short aliases (sys1, sys2, etc.)
- **`HVAC_SYSTEM_TYPE_LOOKUP`**: Maps HVAC strings to type identifiers by building type  
- **`HVAC_SYSTEM_TYPES`**: The list of HVAC types to process

Each has:
- An `ALL_*` version containing complete mappings for all possible HVAC types
- A filtered version containing only mappings for HVAC types present in the data

## Adding New HVAC Types

When new HVAC types are added to the system:

### 1. Update the Enum
Add the new HVAC type to the `HVACSystemType` enum in the appropriate enum file.

### 2. Update the Complete Mappings
In `test_calculator179.py`, update these mappings:

```python
# Add to ALL_HVAC_ALIASES
ALL_HVAC_ALIASES = {
    # existing entries...
    HVACSystemType.NEW_HVAC_TYPE: 'sys7',  # Add new alias
}

# Add to ALL_HVAC_SYSTEM_TYPE_LOOKUP
ALL_HVAC_SYSTEM_TYPE_LOOKUP = {
    'SmallOffice': {
        # existing entries...
        'New HVAC Type String': 'type7',  # Add new mapping
    },
    'RetailStripmall': {
        # existing entries...
        'New HVAC Type String': 'type7',  # Add new mapping
    },
}
```

### 3. That's It!
The script will automatically:
- Detect if the new HVAC type is present in the input data
- Include it in the filtered mappings if it exists
- Skip it if it doesn't exist (no errors)

## Benefits

- **Robust**: Script won't break when spreadsheet contains undefined HVAC types
- **Future-proof**: New HVAC types are automatically included when added properly
- **Maintainable**: Clear separation between complete definitions and dynamic filtering
- **Safe**: Only processes HVAC types that are fully defined in both enum and mappings

## Key Files

- `lib/edge_cases/test_calculator179.py` - Main script with dynamic HVAC handling
- Input spreadsheet with 'hvac' sheet - Defines available HVAC types in data
- Enum definition file - Defines valid HVAC system type enums
