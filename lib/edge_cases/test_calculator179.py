#!/usr/bin/env python
"""Tests for `calculator179`."""
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Tuple

import pandas as pd
import pytest
from strenum import StrEnum
import os
import sys
import logging

if 'CALCULATE_179D' in os.environ:
    sys.path.insert(0, os.environ['CALCULATE_179D'])
else:
    calculator_179d_path = Path(__file__).parent.parent.parent.parent / "BEM-prediction-models"
    sys.path.insert(0, str(calculator_179d_path))

from calculator_179d.data_types import BuildingType, ClimateZone, HVACSystemType
from calculator_179d.main_calculator import calculate_savings
import json

TEST_DIR = Path(__file__).parent
TEST_EXCEL_PATH = TEST_DIR / 'edge_cases.xlsx'
TEST_RESULTS_PATH = TEST_DIR / 'test_results.csv'

FT2_TO_M2 = 0.09290304
# openstudio.convert(1, 'Btu/ft^2*h*R', 'W/m^2*K').get()
U_VALUE_IP_TO_SI = 5.678
ROOF_NON_INSULATION_LAYERS_R_VALUE = 1.11
WALL_NON_INSULATION_LAYERS_R_VALUE = 2.18

DEFAULT_ECONOMICS = {
    "electricity_rate": 11.58,
    "naturalgas_rate": 11.38,
    "energy_tax_deduction_rate_min": 0.5,
    "energy_tax_deduction_rate_max": 1,
    "all_179d_tax_deduction_rate_min": 2.5,
    "all_179d_tax_deduction_rate_max": 5,
    "increment_energy": 0.02,
    "min_threshold_energy": 0.25,
    "increment_all_179d": 0.1,
    "min_threshold_all_179d": 0.25,
}

class BuildingSize(StrEnum):
    SMALL = "small"
    LARGE = "large"

    @property
    def square_feet(self):
        return 1000.0 if self == BuildingSize.SMALL else 24999.0

    @property
    def square_meter(self):
        return self.square_feet * FT2_TO_M2

    def generate_number_of_floors(self) -> List[int]:
        if self.SMALL:
            return [1]
        else:
            return [1, 3]


# for casetype = min savings should be less than min savings limit = -75.
# for casetype = max savings should be less than max savings limit = 150
class CaseType(StrEnum):
    MIN = "min"
    MAX = "max"

    def savings_percent_limit(self,savings_percent):
        if (self.MIN and savings_percent > -75) or (self.MAX and savings_percent < 150.):
           return True
        else:
           return False

def _read_envelope_lookup() -> Dict[Tuple[ClimateZone, CaseType], Dict[str, float]]:
    """Returns a dict with values expected for envelope.

    eg: ```
    {
        (ClimateZone.CZ_1A, CaseType.MAX) : {
          'roof_thermal_perf_type': 0.11356,
          'wall_thermal_perf_type': 0.18926666666666667,
          'window_wall_ratio': 0.01,
          'window_u_factor': 1.1356
        }
    }
    ```
    """
    result = {}

    df = pd.read_excel(
        TEST_EXCEL_PATH,
        sheet_name='envelope',
        index_col=0,
        header=[0, 1],
    ).stack('CaseType', future_stack=True)
    for (cz_str, case_type_str), row in df.iterrows():
        row['roof_r_value_ip']
        converted = {
            'roof_thermal_perf_type': U_VALUE_IP_TO_SI * 1 / row['roof_r_value_ip'],
            'roof_r_value_insulation_layer_ip': row['roof_r_value_ip'] - ROOF_NON_INSULATION_LAYERS_R_VALUE,
            'wall_thermal_perf_type': U_VALUE_IP_TO_SI * 1 / row['wall_r_value_ip'],
            'wall_r_value_insulation_layer_ip': row['wall_r_value_ip'] - WALL_NON_INSULATION_LAYERS_R_VALUE,
            'window_wall_ratio': row['window_to_wall_ratio'],
            'window_u_factor': U_VALUE_IP_TO_SI * row['window_u_value_ip'],
            'window_shgc': row['shgc'],
        }
        result[(ClimateZone(cz_str), CaseType(case_type_str.lower()))] = converted

    return result


def _read_lpd_lookup() -> Dict[Tuple[BuildingType, CaseType], float]:
    """Returns a dict with values expected for lpd in watts per m2.

    eg: ```
    {
        (BuildingType.SMALL_OFFICE, CaseType.MAX) : 4.52,
    }
    ```
    """

    result = {}

    df = pd.read_excel(
        TEST_EXCEL_PATH,
        sheet_name='lpd',
        index_col=0,
        header=[0, 1],
    ).stack('CaseType', future_stack=True)
    for (bt_str, case_type_str), row in df.iterrows():
        result[(BuildingType(bt_str), CaseType(case_type_str.lower()))] = row['lpd_w_per_ft2'] / FT2_TO_M2

    return result


def _read_shw_lookup() -> Dict[Tuple[BuildingType, CaseType], float]:
    result = {}

    df = pd.read_excel(
        TEST_EXCEL_PATH,
        sheet_name='shw',
        index_col=0,
        header=[0, 1],
    ).T
    for (bt_str, case_type_str), row in df.iterrows():
        result[(BuildingType(bt_str), CaseType(case_type_str.lower()))] = {
            k: v for k, v in row.items() if not pd.isna(v)
        }

    return result


def _read_hvac_lookup() -> Dict[Tuple[BuildingType, HVACSystemType, CaseType], float]:
    result = {}

    df = pd.read_excel(
        TEST_EXCEL_PATH,
        sheet_name='hvac',
        index_col=0,
        header=[0, 1, 2],
    ).T
    
    # Get valid HVAC system type values
    valid_hvac_types = [hvac_type.value for hvac_type in HVACSystemType]
    
    for (bt_str, hvac_str, case_type_str), row in df.iterrows():
        # Skip invalid HVAC system types
        if hvac_str not in valid_hvac_types:
            continue
            
        d = {k: v for k, v in row.items() if not pd.isna(v)}
        if 'ERV' in d:
            d.pop('ERV')  # TODO
        result[(BuildingType(bt_str), HVACSystemType(hvac_str), CaseType(case_type_str.lower()))] = d

    return result


def _get_available_hvac_types() -> List[HVACSystemType]:
    """Get HVAC system types that are actually available in the data."""
    # Read the Excel file to see what HVAC types are actually present
    df = pd.read_excel(
        TEST_EXCEL_PATH,
        sheet_name='hvac',
        index_col=0,
        header=[0, 1, 2],
    ).T
    
    valid_hvac_types = [hvac_type.value for hvac_type in HVACSystemType]
    available_hvac_strs = set()
    
    for (bt_str, hvac_str, case_type_str), row in df.iterrows():
        if hvac_str in valid_hvac_types:
            available_hvac_strs.add(hvac_str)
    
    # Convert back to HVACSystemType enum values
    available_hvac_types = []
    for hvac_type in HVACSystemType:
        if hvac_type.value in available_hvac_strs:
            available_hvac_types.append(hvac_type)
    
    return available_hvac_types




ENVELOPE_LOOKUP = _read_envelope_lookup()
LPD_LOOKUP = _read_lpd_lookup()
SHW_LOOKUP = _read_shw_lookup()
HVAC_LOOKUP = _read_hvac_lookup()

# Get available HVAC types from the data
AVAILABLE_HVAC_TYPES = _get_available_hvac_types()

# === DYNAMIC HVAC CONFIGURATION ===
# The following dictionaries are dynamically filtered based on what HVAC types 
# are actually present in the input spreadsheet. This ensures the script is robust
# to changes in the input data without requiring manual updates to these mappings.
#
# To add support for new HVAC types:
# 1. Add the new HVAC type to the HVACSystemType enum
# 2. Add the alias mapping to ALL_HVAC_ALIASES
# 3. Add the system type mappings to ALL_HVAC_SYSTEM_TYPE_LOOKUP
# 4. The script will automatically include them if they appear in the data

BT_ALIASES = {BuildingType.SMALL_OFFICE: 'so', BuildingType.RETAIL_STRIPMALL: 'rs'}

# Define aliases for ALL possible HVAC types (complete mapping)
# When new HVAC types are added to the enum, add them here with their corresponding aliases
ALL_HVAC_ALIASES = {
    HVACSystemType.PSZ_AC_WITH_ELECTRIC_COIL: 'sys2',
    HVACSystemType.PSZ_AC_WITH_GAS_COIL: 'sys3', 
    HVACSystemType.PSZ_HP: 'sys1',
    # Add new HVAC types here when they're added to the enum:
    # HVACSystemType.VRF_DOAS: 'sys4',
    # HVACSystemType.MSHP_DOAS: 'sys5', 
    # HVACSystemType.HP_RTU: 'sys6',
}

# Dynamically filter to only include aliases for HVAC types that exist in the data
HVAC_ALIASES = {hvac_type: alias for hvac_type, alias in ALL_HVAC_ALIASES.items() 
                if hvac_type in AVAILABLE_HVAC_TYPES}

# Test params
ASPECT_RATIOS = [1.0, 6.0]
SHGCS = [0.22, 0.45]

# Use only the HVAC types that are available in the input data (dynamically determined)
HVAC_SYSTEM_TYPES = AVAILABLE_HVAC_TYPES

BUILDING_TYPES = [x for x in BuildingType]
CLIMATE_ZONES = [x for x in ClimateZone]
CASE_TYPES = [x for x in CaseType]
BUILDING_SIZES = [x for x in BuildingSize]


RESULTS_INDEX_NAMES = [
    'BuildingType',
    'HVACSystemType',
    'ClimateZone',
    'BuildingSize',
    'CaseType',
    'Aspect Ratio',
    'Number of Floors',
]

RESULTS = pd.read_csv(
    TEST_RESULTS_PATH, index_col=list(range(len(RESULTS_INDEX_NAMES))), dtype={k: str for k in RESULTS_INDEX_NAMES}
)


@dataclass
class SingleTestInfo:
    building_type: BuildingType
    hvac_type: HVACSystemType
    climate_zone: ClimateZone
    building_size: BuildingSize
    case_type: CaseType
    aspect_ratio: float
    number_of_floors: int


    def name(self) -> str:
        return (
            f"sqft{self.building_size.square_feet}_{BT_ALIASES[self.building_type]}_{HVAC_ALIASES[self.hvac_type]}_CZ{self.climate_zone}_"
            f"ar{self.aspect_ratio}_nf{self.number_of_floors}_{self.case_type}"
        )

    def index(self) -> str:
        return tuple(
            str(x)
            for x in (
                self.building_type,
                self.hvac_type,
                self.climate_zone,
                self.building_size,
                self.case_type,
                self.aspect_ratio,
                self.number_of_floors,
            )
        )

    def envelope_info(self) -> Dict[str, float]:
        return ENVELOPE_LOOKUP[(self.climate_zone, self.case_type)]

    def shw_info(self) -> Dict[str, float]:
        """None for non gas."""
        if not self.hvac_type.is_gas:  # TODO; really?
            return {}

        return SHW_LOOKUP[(self.building_type, self.case_type)]

    def lpd(self) -> float:
        return LPD_LOOKUP[(self.building_type, self.case_type)]

    def hvac_info(self) -> Dict[str, float]:
        return HVAC_LOOKUP[(self.building_type, self.hvac_type, self.case_type)]

    def to_json(self):
        return {
            'building_type': str(self.building_type),
            'climate_zone': str(self.climate_zone),
            'hvac_system': str(self.hvac_type),
            'gross_floor_area': self.building_size.square_meter,
            'number_of_floors': self.number_of_floors,
            'aspect_ratio': self.aspect_ratio,
            **self.envelope_info(),
            'proposed_lpd': self.lpd(),
            **self.shw_info(),
            **self.hvac_info(),
            **DEFAULT_ECONOMICS,
        }

    def to_param(self):
        return pytest.param(self, id=self.name())

    def expected_results(self):
        return RESULTS.loc[self.index()]


def get_all_tests():
    all_tests: List[SingleTestInfo] = []
    for building_type in BUILDING_TYPES:
        for hvac_type in HVAC_SYSTEM_TYPES:
            for climate_zone in CLIMATE_ZONES:
                for aspect_ratio in ASPECT_RATIOS:
                    for case_type in CASE_TYPES:
                        for building_size in BUILDING_SIZES:
                            for number_of_floors in building_size.generate_number_of_floors():
                                all_tests.append(
                                    SingleTestInfo(
                                        building_type=building_type,
                                        hvac_type=hvac_type,
                                        climate_zone=climate_zone,
                                        building_size=building_size,
                                        case_type=case_type,
                                        aspect_ratio=aspect_ratio,
                                        number_of_floors=number_of_floors,
                                    )
                                )
    return all_tests


@pytest.mark.parametrize(
   "test_info",
   [t.to_param() for t in get_all_tests()],
)
def test_calculate_savings(test_info: Dict[str, Any]):
    results = calculate_savings(property_info=test_info.to_json())

    assert test_info.case_type.savings_percent_limit(
        results['percent_savings_actual']
    )
    expected_results = test_info.expected_results().to_dict()

    assert sorted(results.keys()) == sorted(expected_results.keys())
    for k, expected in expected_results.items():
        actual = results[k]
        assert actual == pytest.approx(expected), f"Difference for {k}"


def _regenerate_test_results():
        results = {}
        statuses = {}
        for t in get_all_tests():
            d = {}
            sim_ok = True
            error_msg = None
            try:
                d = calculate_savings(t.to_json())
            except Exception as e:
                sim_ok = False
                error_msg = f"{type(e).__name__}: {e}"

            index = t.index()

            results[index] = d
            statuses[index] = {'status': sim_ok, 'error': error_msg}

        df = pd.DataFrame(results).T
        df.index.names = RESULTS_INDEX_NAMES

        for col in df.columns:
            df[col] = pd.to_numeric(df[col])

        df_status = pd.DataFrame(statuses).T
        df_status.index.names = RESULTS_INDEX_NAMES
        failed = df_status[df_status['status'] == False]
        if not failed.empty:
            raise ValueError(f"{len(failed)} simulation failed: {failed}")

        df.to_csv(TEST_RESULTS_PATH)

# Define mappings for ALL possible HVAC types and building types (complete mapping)
# When new HVAC types or building types are added, update this lookup
ALL_HVAC_SYSTEM_TYPE_LOOKUP = {
    'SmallOffice': {
        'PSZ-HP': 'type1',
        'PSZ-AC with electric coil': 'type2',
        'PSZ-AC with gas coil': 'type3',
        'VRF DOAS': 'type4',
        'MSHP DOAS': 'type5',
        'HP RTU': 'type6',
    },
    'RetailStripmall': {
        'PSZ-HP': 'type1',
        'PSZ-AC with electric coil': 'type2',
        'PSZ-AC with gas coil': 'type3',
        'VRF DOAS': 'type4',
        'MSHP DOAS': 'type5',
        'HP RTU': 'type6',
    },
    # Add more building types as needed
}

# Dynamically filter to only include HVAC types that exist in the actual data
HVAC_SYSTEM_TYPE_LOOKUP = {}
available_hvac_strs = [hvac_type.value for hvac_type in AVAILABLE_HVAC_TYPES]

for building_type, hvac_mappings in ALL_HVAC_SYSTEM_TYPE_LOOKUP.items():
    HVAC_SYSTEM_TYPE_LOOKUP[building_type] = {
        hvac_str: type_mapping for hvac_str, type_mapping in hvac_mappings.items()
        if hvac_str in available_hvac_strs
    }


def map_hvac_system_type(building_type: str, hvac_type: str) -> str:
    """Map HVAC system type to the corresponding type identifier for a specific building type.
    
    Args:
        building_type: The building type string (e.g., 'SmallOffice')
        hvac_type: The HVAC system type string (e.g., 'PSZ-HP')
        
    Returns:
        The mapped type identifier (e.g., 'type1') or the original hvac_type if no mapping exists
    """
    mapped_type = HVAC_SYSTEM_TYPE_LOOKUP.get(building_type, {}).get(hvac_type, hvac_type)
    if mapped_type == hvac_type and building_type in HVAC_SYSTEM_TYPE_LOOKUP:
        logging.debug(f"No mapping found for HVAC type '{hvac_type}' in building type '{building_type}'")
    return mapped_type

def format_building_type(building_type: BuildingType) -> str:
    """Convert BuildingType enum to formatted string (e.g., SMALL_OFFICE -> SmallOffice)."""
    return ''.join(word.capitalize() for word in building_type.name.split('_'))

def export_to_json(output_path: str):
    data = {
        "building_types": {}
    }

    for building_type in BUILDING_TYPES:
        bt_key = format_building_type(building_type)
        data["building_types"][bt_key] = {"hvac_system_types": {}}

        for hvac_type in HVAC_SYSTEM_TYPES:
            hvac_key = map_hvac_system_type(bt_key, str(hvac_type))
            print(f"Mapping HVAC system type for building type '{bt_key}' and HVAC type '{hvac_type}': {hvac_key}")
            data["building_types"][bt_key]["hvac_system_types"][hvac_key] = {"climate_zones": {}}

            for climate_zone in CLIMATE_ZONES:
                cz_key = str(climate_zone)
                data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key] = {
                    "parameters_on_edges": {},
                    "parameters_fixed": {},
                    "parameters_range": {},
                    "analysis_settings": {
                        "Total number of samples": {
                            "setting_name": "algorithm_setting",
                            "argument_name": "number_of_samples",
                            "values": 100000
                        }
                    }
                }

                for case_type in CASE_TYPES:
                    for building_size in BUILDING_SIZES:
                        for aspect_ratio in ASPECT_RATIOS:
                            for number_of_floors in building_size.generate_number_of_floors():
                                test_info = SingleTestInfo(
                                    building_type=building_type,
                                    hvac_type=hvac_type,
                                    climate_zone=climate_zone,
                                    building_size=building_size,
                                    case_type=case_type,
                                    aspect_ratio=aspect_ratio,
                                    number_of_floors=number_of_floors,
                                )

                                envelope_info_min = ENVELOPE_LOOKUP[(climate_zone, CaseType.MIN)]
                                envelope_info_max = ENVELOPE_LOOKUP[(climate_zone, CaseType.MAX)]
                                shw_info = test_info.shw_info()
                                lpd_min = LPD_LOOKUP[(building_type, CaseType.MIN)]
                                lpd_max = LPD_LOOKUP[(building_type, CaseType.MAX)]
                                hvac_info_min = HVAC_LOOKUP[(building_type, hvac_type, CaseType.MIN)]
                                hvac_info_max = HVAC_LOOKUP[(building_type, hvac_type, CaseType.MAX)]

                                # Populate parameters_on_edges
                                data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_on_edges"] = {
                                    "Aspect Ratio": {
                                        "measure_name": "create_bar_from_building_type_ratios_comstock",
                                        "argument_name": "ns_to_ew_ratio"
                                    },
                                    "SHGC": {
                                        "measure_name": "replace_baseline_windows_Rvalues",
                                        "argument_name": "shgc"
                                    }
                                }

                                # Populate parameters_fixed
                                data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_fixed"] = {
                                    "Gross floor area": {
                                        "measure_name": "create_bar_from_building_type_ratios_comstock",
                                        "argument_name": "total_bldg_floor_area",
                                        "values": [1000, 24999]
                                    },
                                    "Number of Floors": {
                                        "measure_name": "create_bar_from_building_type_ratios_comstock",
                                        "argument_name": "num_stories_above_grade",
                                        "values": [1, 3]
                                    },
                                    "Building Rotation": {
                                        "measure_name": "create_bar_from_building_type_ratios_comstock",
                                        "argument_name": "building_rotation",
                                        "values": [0]
                                    },
                                    "SWH Eff Cat Ratio": {
                                        "measure_name": "set_water_heater_efficiency",
                                        "argument_name": "performance_category_ratio",
                                        "values": [0]
                                    },
                                    "SWH Eff Cap Ratio": {
                                        "measure_name": "set_water_heater_efficiency",
                                        "argument_name": "capacity_btu_per_hr_ratio",
                                        "values": [0]
                                    },
                                    "SWH Eff FHR Ratio": {
                                        "measure_name": "set_water_heater_efficiency",
                                        "argument_name": "first_hour_rating_ratio",
                                        "values": [0]
                                    }
                                }

                                # Populate parameters_range
                                data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_range"] = {
                                    "Roof R-value": {
                                        "measure_name": "IncreaseInsulationRValueForRoofs",
                                        "argument_name": "r_value",
                                        "values": [(envelope_info_min['roof_r_value_insulation_layer_ip']), (envelope_info_max['roof_r_value_insulation_layer_ip'])]
                                    },
                                    "Wall R-value": {
                                        "measure_name": "IncreaseInsulationRValueForExteriorWalls",
                                        "argument_name": "r_value",
                                        "values": [(envelope_info_min['wall_r_value_insulation_layer_ip']), (envelope_info_max['wall_r_value_insulation_layer_ip'])]
                                    },
                                    "Window U-value": {
                                        "measure_name": "replace_baseline_windows_Rvalues",
                                        "argument_name": "u_value_ip",
                                        "values": [envelope_info_min['window_u_factor'] / U_VALUE_IP_TO_SI, envelope_info_max['window_u_factor'] / U_VALUE_IP_TO_SI]
                                    },
                                    "LPD": {
                                        "measure_name": "SetLightingLoadsByLPD",
                                        "argument_name": "lpd",
                                        "values": [lpd_min * FT2_TO_M2, lpd_max * FT2_TO_M2]
                                    },
                                    "Window to Wall Ratio": {
                                        "measure_name": "create_bar_from_building_type_ratios_comstock",
                                        "argument_name": "wwr",
                                        "values": [envelope_info_min['window_wall_ratio'], envelope_info_max['window_wall_ratio']]
                                    },
                                    "ERV Applied": {
                                        "measure_name": "upgrade_hvac_exhaust_air_energy_or_heat_recovery",
                                        "argument_name": "__SKIP__",
                                        "values": ["TRUE", "FALSE"]
                                    }
                                }

                                hp_htg_eff = 'heating_cop'

                                if hp_htg_eff in hvac_info_min and hp_htg_eff in hvac_info_max:
                                    data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_range"]["Heating Efficiency"] = {
                                        "measure_name": "set_heating_cooling_cop_efficiency",
                                        "argument_name": "heating_cop",
                                        "values": [hvac_info_min[hp_htg_eff], hvac_info_max[hp_htg_eff]]
                                    }

                                gas_htg_eff = 'gas_coil_average_efficiency'

                                if gas_htg_eff in hvac_info_min and gas_htg_eff in hvac_info_max:
                                    data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_range"]["Heating Efficiency"] = {
                                        "measure_name": "set_heating_cooling_cop_efficiency",
                                        "argument_name": "efficiency",
                                        "values": [hvac_info_min[gas_htg_eff], hvac_info_max[gas_htg_eff]]
                                    }

                                key_clg_eff = 'cooling_cop'

                                if key_clg_eff in hvac_info_min and key_clg_eff in hvac_info_max:
                                    data["building_types"][bt_key]["hvac_system_types"][hvac_key]["climate_zones"][cz_key]["parameters_range"]["Cooling Efficiency"] = {
                                        "measure_name": "set_heating_cooling_cop_efficiency",
                                        "argument_name": "cooling_cop",
                                        "values": [hvac_info_min[key_clg_eff], hvac_info_max[key_clg_eff]]
                                    }

    with open(output_path, 'w') as f:
        json.dump(data, f, indent=2)

if __name__ == "__main__":
    output_path = Path(__file__).parent.parent.parent / "files" / "edge_samples_configs.json"
    export_to_json(output_path)
