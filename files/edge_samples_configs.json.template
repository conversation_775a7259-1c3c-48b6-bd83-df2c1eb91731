{"building_types": {"SmallOffice": {"hvac_system_types": {"type1": {"climate_zones": {"1A": {"parameters_on_edges": {"Aspect Ratio": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "ns_to_ew_ratio"}, "SHGC": {"measure_name": "replace_baseline_windows_Rvalues", "argument_name": "shgc"}}, "parameters_fixed": {"Gross floor area": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "total_bldg_floor_area", "values": [1000, 24999]}, "Number of Floors": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "num_stories_above_grade", "values": [1, 3]}, "Building Rotation": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "building_rotation", "values": [0]}, "SWH Eff Cat Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "performance_category_ratio", "values": [0]}, "SWH Eff Cap Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "capacity_btu_per_hr_ratio", "values": [0]}, "SWH Eff FHR Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "first_hour_rating_ratio", "values": [0]}}, "parameters_range": {"Roof R-value": {"measure_name": "IncreaseInsulationRValueForRoofs", "argument_name": "r_value", "values": [14.408, 51.9]}, "Wall R-value": {"measure_name": "IncreaseInsulationRValueForExteriorWalls", "argument_name": "r_value", "values": [12.498, 31.19]}, "Window U-value": {"measure_name": "replace_baseline_windows_Rvalues", "argument_name": "u_value_ip", "values": [0.468, 0.192]}, "LPD": {"measure_name": "SetLightingLoadsByLPD", "argument_name": "lpd", "values": [1, 0.403]}, "Heating Efficiency": {"measure_name": "set_heating_cooling_cop_efficiency", "argument_name": "heating_cop", "values": [2.774, 6.239]}, "Cooling Efficiency": {"measure_name": "set_heating_cooling_cop_efficiency", "argument_name": "cooling_cop", "values": [2.85, 6.238]}, "Window to Wall Ratio": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "wwr", "values": [0.416, 0.01]}, "ERV Applied": {"measure_name": "upgrade_hvac_exhaust_air_energy_or_heat_recovery", "argument_name": "__SKIP__", "values": ["TRUE", "FALSE"]}}, "analysis_settings": {"Total number of samples": {"setting_name": "algorithm_setting", "argument_name": "number_of_samples", "values": 100000}}}}}, "type2": {"climate_zones": {"1A": {"parameters_on_edges": {"Aspect Ratio": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "ns_to_ew_ratio"}, "SHGC": {"measure_name": "replace_baseline_windows_Rvalues", "argument_name": "shgc"}}, "parameters_fixed": {"Gross floor area": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "total_bldg_floor_area", "values": [1000, 24999]}, "Number of Floors": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "num_stories_above_grade", "values": [1, 3]}, "Building Rotation": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "building_rotation", "values": [0]}, "SWH Eff Cat Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "performance_category_ratio", "values": [0]}, "SWH Eff Cap Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "capacity_btu_per_hr_ratio", "values": [0]}, "SWH Eff FHR Ratio": {"measure_name": "set_water_heater_efficiency", "argument_name": "first_hour_rating_ratio", "values": [0]}}, "parameters_range": {"Roof R-value": {"measure_name": "IncreaseInsulationRValueForRoofs", "argument_name": "r_value", "values": [14.408, 51.9]}, "Wall R-value": {"measure_name": "IncreaseInsulationRValueForExteriorWalls", "argument_name": "r_value", "values": [12.498, 31.19]}, "Window U-value": {"measure_name": "replace_baseline_windows_Rvalues", "argument_name": "u_value_ip", "values": [0.468, 0.192]}, "LPD": {"measure_name": "SetLightingLoadsByLPD", "argument_name": "lpd", "values": [1, 0.403]}, "Cooling Efficiency": {"measure_name": "set_heating_cooling_cop_efficiency", "argument_name": "cooling_cop", "values": [2.85, 6.238]}, "Window to Wall Ratio": {"measure_name": "create_bar_from_building_type_ratios_comstock", "argument_name": "wwr", "values": [0.416, 0.01]}, "ERV Applied": {"measure_name": "upgrade_hvac_exhaust_air_energy_or_heat_recovery", "argument_name": "__SKIP__", "values": ["TRUE", "FALSE"]}}, "analysis_settings": {"Total number of samples": {"setting_name": "algorithm_setting", "argument_name": "number_of_samples", "values": 100000}}}}}}}}}