Measure Name,Argument Name,Default Value,Samples_as_Array_Batch5017_proposed_training,Sample_Type_Batch5017_proposed_training
simulation_settings,timesteps_per_hr,4,,
simulation_settings,enable_dst,TRUE,,
simulation_settings,dst_start,2nd Sunday in March,,
simulation_settings,dst_end,1st Sunday in November,,
simulation_settings,calendar_year,0,,
simulation_settings,jan_first_day_of_wk,Thursday,,
simulation_settings,begin_month,1,,
simulation_settings,begin_day,1,,
simulation_settings,end_month,12,,
simulation_settings,end_day,31,,
simulation_settings,output_diagnostics,FALSE,,
simulation_settings,__SKIP__,FALSE,,
ChangeBuildingLocation_179d_gem,weather_file_name,USA_UT_Salt.Lake.City.Intl.AP.725720_TMY3.epw,USA_MD_Baltimore-Washington.Intl.AP.724060_TMY3.epw,
ChangeBuildingLocation_179d_gem,set_year,0,,
ChangeBuildingLocation_179d_gem,use_upstream_args,TRUE,,
ChangeBuildingLocation_179d_gem,epw_gsub,Do Nothing,,
ChangeBuildingLocation_179d_gem,__SKIP__,FALSE,,
create_bar_from_building_type_ratios_comstock,bldg_type_a,SmallOffice,SmallHotel,
create_bar_from_building_type_ratios_comstock,bldg_subtype_a,,,
create_bar_from_building_type_ratios_comstock,bldg_type_a_num_units,1,,
create_bar_from_building_type_ratios_comstock,bldg_type_b,SmallOffice,,
create_bar_from_building_type_ratios_comstock,bldg_subtype_b,,,
create_bar_from_building_type_ratios_comstock,bldg_type_b_fract_bldg_area,0,,
create_bar_from_building_type_ratios_comstock,bldg_type_b_num_units,1,,
create_bar_from_building_type_ratios_comstock,bldg_type_c,SmallOffice,,
create_bar_from_building_type_ratios_comstock,bldg_subtype_c,,,
create_bar_from_building_type_ratios_comstock,bldg_type_c_fract_bldg_area,0,,
create_bar_from_building_type_ratios_comstock,bldg_type_c_num_units,1,,
create_bar_from_building_type_ratios_comstock,bldg_type_d,SmallOffice,,
create_bar_from_building_type_ratios_comstock,bldg_subtype_d,,,
create_bar_from_building_type_ratios_comstock,bldg_type_d_fract_bldg_area,0,,
create_bar_from_building_type_ratios_comstock,bldg_type_d_num_units,1,,
create_bar_from_building_type_ratios_comstock,single_floor_area,0,,
create_bar_from_building_type_ratios_comstock,total_bldg_floor_area,10000,"[1000.0, 750000.0, 200]",stats
create_bar_from_building_type_ratios_comstock,floor_height,0,,
create_bar_from_building_type_ratios_comstock,custom_height_bar,TRUE,,
create_bar_from_building_type_ratios_comstock,num_stories_above_grade,1,"[1, 2, 3, 4, 5]",
create_bar_from_building_type_ratios_comstock,num_stories_below_grade,0,,
create_bar_from_building_type_ratios_comstock,building_rotation,0,"[0.0, 187.2, 200]",stats
create_bar_from_building_type_ratios_comstock,template,90.1-2007,90.1-2007,
create_bar_from_building_type_ratios_comstock,ns_to_ew_ratio,2.14,"[0.96, 6.24, 200]",stats
create_bar_from_building_type_ratios_comstock,perim_mult,0,,
create_bar_from_building_type_ratios_comstock,bar_width,0,,
create_bar_from_building_type_ratios_comstock,bar_sep_dist_mult,10,,
create_bar_from_building_type_ratios_comstock,wwr,0.18,"[0.01, 0.4, 200]",stats
create_bar_from_building_type_ratios_comstock,party_wall_fraction,0,,
create_bar_from_building_type_ratios_comstock,party_wall_stories_north,0,,
create_bar_from_building_type_ratios_comstock,party_wall_stories_south,0,,
create_bar_from_building_type_ratios_comstock,party_wall_stories_east,0,,
create_bar_from_building_type_ratios_comstock,party_wall_stories_west,0,,
create_bar_from_building_type_ratios_comstock,neighbor_height_method,Absolute,,
create_bar_from_building_type_ratios_comstock,building_height_relative_to_neighbors,0,,
create_bar_from_building_type_ratios_comstock,neighbor_height_north,0,,
create_bar_from_building_type_ratios_comstock,neighbor_height_south,0,,
create_bar_from_building_type_ratios_comstock,neighbor_height_east,0,,
create_bar_from_building_type_ratios_comstock,neighbor_height_west,0,,
create_bar_from_building_type_ratios_comstock,neighbor_offset_north,0,,
create_bar_from_building_type_ratios_comstock,neighbor_offset_south,0,,
create_bar_from_building_type_ratios_comstock,neighbor_offset_east,0,,
create_bar_from_building_type_ratios_comstock,neighbor_offset_west,0,,
create_bar_from_building_type_ratios_comstock,bottom_story_ground_exposed_floor,TRUE,,
create_bar_from_building_type_ratios_comstock,top_story_exterior_exposed_roof,TRUE,,
create_bar_from_building_type_ratios_comstock,story_multiplier,Basements Ground Mid Top,Basements Ground Mid Top,
create_bar_from_building_type_ratios_comstock,make_mid_story_surfaces_adiabatic,FALSE,TRUE,
create_bar_from_building_type_ratios_comstock,bar_division_method,Multiple Space Types - Individual Stories Sliced,,
create_bar_from_building_type_ratios_comstock,double_loaded_corridor,Primary Space Type,,
create_bar_from_building_type_ratios_comstock,space_type_sort_logic,Building Type > Size,,
create_bar_from_building_type_ratios_comstock,use_upstream_args,TRUE,,
create_bar_from_building_type_ratios_comstock,climate_zone,Lookup From Stat File,,
create_bar_from_building_type_ratios_comstock,__SKIP__,FALSE,,
create_typical_building_from_model_comstock,template,179D 90.1-2007,179D 90.1-2007,
create_typical_building_from_model_comstock,system_type,Inferred,PSZ-AC with electric coil,
create_typical_building_from_model_comstock,hvac_delivery_type,Forced Air,,
create_typical_building_from_model_comstock,htg_src,NaturalGas,,
create_typical_building_from_model_comstock,clg_src,Electricity,,
create_typical_building_from_model_comstock,swh_src,Inferred,"[Electricity, NaturalGas]",
create_typical_building_from_model_comstock,kitchen_makeup,Adjacent,,
create_typical_building_from_model_comstock,exterior_lighting_zone,3 - All Other Areas,,
create_typical_building_from_model_comstock,add_constructions,TRUE,,
create_typical_building_from_model_comstock,wall_construction_type,Inferred,SteelFramed,
create_typical_building_from_model_comstock,add_space_type_loads,TRUE,,
create_typical_building_from_model_comstock,add_elevators,TRUE,,
create_typical_building_from_model_comstock,add_internal_mass,TRUE,,
create_typical_building_from_model_comstock,add_exterior_lights,TRUE,FALSE,
create_typical_building_from_model_comstock,onsite_parking_fraction,1,0,
create_typical_building_from_model_comstock,add_exhaust,TRUE,,
create_typical_building_from_model_comstock,add_swh,TRUE,,
create_typical_building_from_model_comstock,add_thermostat,TRUE,,
create_typical_building_from_model_comstock,add_hvac,TRUE,,
create_typical_building_from_model_comstock,add_refrigeration,TRUE,,
create_typical_building_from_model_comstock,modify_wkdy_op_hrs,FALSE,,
create_typical_building_from_model_comstock,wkdy_op_hrs_start_time,8,,
create_typical_building_from_model_comstock,wkdy_op_hrs_duration,8,,
create_typical_building_from_model_comstock,modify_wknd_op_hrs,FALSE,,
create_typical_building_from_model_comstock,wknd_op_hrs_start_time,8,,
create_typical_building_from_model_comstock,wknd_op_hrs_duration,8,,
create_typical_building_from_model_comstock,unmet_hours_tolerance,1,,
create_typical_building_from_model_comstock,remove_objects,TRUE,,
create_typical_building_from_model_comstock,use_upstream_args,TRUE,,
create_typical_building_from_model_comstock,climate_zone,Lookup From Model,,
create_typical_building_from_model_comstock,__SKIP__,FALSE,,
IncreaseInsulationRValueForRoofs,r_value,30,"[18.912, 52.0, 200]",stats
IncreaseInsulationRValueForRoofs,allow_reduction,FALSE,TRUE,
IncreaseInsulationRValueForRoofs,material_cost_increase_ip,0,,
IncreaseInsulationRValueForRoofs,one_time_retrofit_cost_ip,0,,
IncreaseInsulationRValueForRoofs,years_until_retrofit_cost,0,,
IncreaseInsulationRValueForRoofs,__SKIP__,FALSE,,
IncreaseInsulationRValueForExteriorWalls,r_value,13,"[12.864, 31.2, 200]",stats
IncreaseInsulationRValueForExteriorWalls,allow_reduction,FALSE,TRUE,
IncreaseInsulationRValueForExteriorWalls,material_cost_increase_ip,0,,
IncreaseInsulationRValueForExteriorWalls,one_time_retrofit_cost_ip,0,,
IncreaseInsulationRValueForExteriorWalls,years_until_retrofit_cost,0,,
IncreaseInsulationRValueForExteriorWalls,__SKIP__,FALSE,,
SetLightingLoadsByLPD,lpd,1,"[0.21, 1.04, 200]",stats
SetLightingLoadsByLPD,__SKIP__,FALSE,,
replace_baseline_windows_Rvalues,window_pane_type,Selected Values,,
replace_baseline_windows_Rvalues,fenestration_frame_type,Metal Framing with Thermal Break,,
replace_baseline_windows_Rvalues,u_value_ip,1.01,"[0.192, 0.468, 200]",stats
replace_baseline_windows_Rvalues,shgc,0.744,"[0.215, 0.468, 200]",stats
replace_baseline_windows_Rvalues,vlt,0.754,,
replace_baseline_windows_Rvalues,__SKIP__,FALSE,,
179d_gem_baseline_hvac_control,standard,179D 90.1-2007,179D 90.1-2007,
179d_gem_baseline_hvac_control,building_type,SmallOffice,,
179d_gem_baseline_hvac_control,climate_zone,ASHRAE 169-2013-2A,,
179d_gem_baseline_hvac_control,custom,*None*,,
179d_gem_baseline_hvac_control,debug,FALSE,,
179d_gem_baseline_hvac_control,use_upstream_args,TRUE,,
179d_gem_baseline_hvac_control,__SKIP__,FALSE,TRUE,
set_heating_cooling_cop_efficiency,cooling_cop,3.7,,
set_heating_cooling_cop_efficiency,heating_cop,3.7,,
set_heating_cooling_cop_efficiency,efficiency,0.8,,
set_heating_cooling_cop_efficiency,boiler_efficiency,0.8,,
set_heating_cooling_cop_efficiency,chiller_cop,5.3,,
set_heating_cooling_cop_efficiency,__SKIP__,FALSE,TRUE,
set_water_heater_efficiency,performance_category_ratio,1,"[0.0, 1.0, 200]",stats
set_water_heater_efficiency,capacity_btu_per_hr_ratio,0.5,"[0.0, 1.0, 200]",stats
set_water_heater_efficiency,first_hour_rating_ratio,0.5,"[0.0, 1.0, 200]",stats
set_water_heater_efficiency,__SKIP__,FALSE,,
upgrade_hvac_vrf_hr_doas,vrf_defrost_strategy,reverse-cycle,,
upgrade_hvac_vrf_hr_doas,disable_defrost,FALSE,,
upgrade_hvac_vrf_hr_doas,upsizing_allowance_pct,0,,
upgrade_hvac_vrf_hr_doas,apply_measure,TRUE,,
upgrade_hvac_vrf_hr_doas,__SKIP__,FALSE,TRUE,
upgrade_hvac_exhaust_air_energy_or_heat_recovery,__SKIP__,FALSE,TRUE,
upgrade_hvac_doas_hp_minisplits,area_limit_sf,20000,,
upgrade_hvac_doas_hp_minisplits,doas_htg_fuel,electric_resistance,,
upgrade_hvac_doas_hp_minisplits,performance_oversizing_factor,0.35,,
upgrade_hvac_doas_hp_minisplits,__SKIP__,FALSE,TRUE,
upgrade_hvac_add_heat_pump_rtu,backup_ht_fuel_scheme,electric_resistance_backup,,
upgrade_hvac_add_heat_pump_rtu,performance_oversizing_factor,0,,
upgrade_hvac_add_heat_pump_rtu,htg_sizing_option,0F,,
upgrade_hvac_add_heat_pump_rtu,clg_oversizing_estimate,1,,
upgrade_hvac_add_heat_pump_rtu,htg_to_clg_hp_ratio,1,,
upgrade_hvac_add_heat_pump_rtu,std_perf,FALSE,,
upgrade_hvac_add_heat_pump_rtu,hr,FALSE,,
upgrade_hvac_add_heat_pump_rtu,dcv,FALSE,,
upgrade_hvac_add_heat_pump_rtu,econ,FALSE,,
upgrade_hvac_add_heat_pump_rtu,__SKIP__,FALSE,FALSE,
nze_hvac,remove_existing_hvac,FALSE,,
nze_hvac,hvac_system_type,DOAS with fan coil chiller with central air source heat pump,,
nze_hvac,doas_dcv,FALSE,,
nze_hvac,hvac_system_partition,Automatic Partition,,
nze_hvac,__SKIP__,FALSE,TRUE,
hardsize_model,apply_hardsize,TRUE,,
hardsize_model,__SKIP__,FALSE,FALSE,
reporting_179_d_support,disable_upstream_arg,FALSE,,
reporting_179_d_support,hvac_type_from_upgrade,FALSE,TRUE,
reporting_179_d_support,__SKIP__,FALSE,,
create_179d_gem_baseline_building,standard,179D 90.1-2007,,
create_179d_gem_baseline_building,building_type,SmallOffice,,
create_179d_gem_baseline_building,climate_zone,ASHRAE 169-2013-2A,,
create_179d_gem_baseline_building,custom,*None*,,
create_179d_gem_baseline_building,debug,FALSE,,
create_179d_gem_baseline_building,use_upstream_args,TRUE,,
create_179d_gem_baseline_building,__SKIP__,FALSE,TRUE,
remove_additional_lighting,__SKIP__,FALSE,TRUE,
swh_patch,capacity_btu_per_hr_ratio,0.5,,
swh_patch,first_hour_rating_ratio,0.5,,
swh_patch,use_upstream_arg,TRUE,,
swh_patch,__SKIP__,FALSE,TRUE,
hardsize_model_copy,apply_hardsize,TRUE,,
hardsize_model_copy,__SKIP__,FALSE,TRUE,
reporting_179_d,disable_upstream_arg,FALSE,,
reporting_179_d,add_warning_severe_counts,FALSE,TRUE,
reporting_179_d,__SKIP__,FALSE,,
ServerDirectoryCleanup,sql,TRUE,,
ServerDirectoryCleanup,eso,TRUE,,
ServerDirectoryCleanup,audit,TRUE,,
ServerDirectoryCleanup,osm,TRUE,,
ServerDirectoryCleanup,idf,TRUE,,
ServerDirectoryCleanup,bnd,TRUE,,
ServerDirectoryCleanup,eio,TRUE,,
ServerDirectoryCleanup,shd,TRUE,,
ServerDirectoryCleanup,mdd,TRUE,,
ServerDirectoryCleanup,rdd,TRUE,,
ServerDirectoryCleanup,csv,TRUE,,
ServerDirectoryCleanup,Sizing Run Directories,FALSE,,
ServerDirectoryCleanup,__SKIP__,FALSE,,
algorithm_setting,seed,179,179,
algorithm_setting,number_of_samples,1000,1000,
